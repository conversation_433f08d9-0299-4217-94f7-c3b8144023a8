<?xml version="1.0" encoding="utf-8"?>
<merchantHtmlPacketModel xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="Paymetric:XiIntercept:MerchantHtmlPacketModel">
  <iFramePacket>
    <hostUri>https://stgxm-shopping.4changeenergy.com</hostUri> 
    <cssUri>https://cms.txu.com/~/media/Themes/VistraApps/Shopping/paymetric.css?v=6.3</cssUri>
  </iFramePacket>
  <merchantHtml>
    <htmlSection class="ppsTokenizer" xmlns="Paymetric:XiIntercept:MerchantHtmlPacketModel">      
    <tag name="span" class="indicates-a-require">* Indicates a required field</tag>
      <cardIndicatorSection>

        <tag class="form-block flex flex-wrap"> 
          <tag class="form-field txt-baseGrey">
            <tag name="span" class="nickname-block flex-inline items-center">
              <label for="cardNumber" text="Card Number" />
              <tag name="span" class="req inline_req">*</tag>
              <tag class="pos_rel inline-div payment-icons">
              <cardTypeIndicator class="cardIndicator">
                <items>
                  <item for="american express" class="axIndicator" ></item>
                  <item for="mastercard" class="mcIndicator" ></item>
                  <item for="visa" class="viIndicator" ></item>
				  <item for="discover" class="diIndicator" ></item>
                </items>
              </cardTypeIndicator>
            </tag>
            </tag>
            <tag class="col-xs-12 pos_rel inline-div">
              <tboxCardNumber tokenize="true"  class="no_cursor text ppsTokenizerCard ccName tlBlock required" luhn-check="true" digits-only="true" digits-only-msg="Card Number can't contain alphabets or special characters" 	 maxlength="19" maxlength-msg="Card Number must be 15 to 19 digits" minlength="15" minlength-msg="Card Number must be 15 to 19 digits"  pattern="^[0-9]{15,19}$" required-msg="Card Number is required." pattern-msg="CC Number Error Message"  luhn-check-msg="Please enter valid Card Number"/>
              <tag name="span" class="error">
                <validationMsg for="CardNumber" />
              </tag>
            </tag>
          </tag>
          <tag class="form-field txt-baseGrey">
            <tag name="span" class="">
              <label for="cardholderName" text="Card Holder Name" />
              <tag name="span" class="req inline_req">*</tag>
            </tag>
            <tag class="col-xs-12 pos_rel">
              <textBox name="cardholderName" class="form-control" pattern="^[a-zA-Z]+[\s|-]?[a-zA-Z']+[\s|-]?[a-zA-Z]{1,25}$" maxlength="25" maxlength-msg="Card Holder Name must be 25 characters"  required-msg="CC Name Error Message" pattern-msg="Invalid CardHolderName Error Message" />
              <tag name="span" class="error">
                <validationMsg for="cardholderName" />
              </tag>
            </tag>
          </tag>
        </tag>
        <tag class="form-block flex flex-wrap col-xs-12">
          <tag class="form-field col-xs-12 col-md-4 max-w-280">
            <tag name="div" class="grey_text">
              <label for="expDate" text="Expiration Date" />
              <tag name="span" class="req inline_req">*</tag>
            </tag>
            <tag class="item form-field month-day-selector col-md-6 col-sm-6 col-xs-12 margin_0">
              <tag name="div" class="d_flex">
                <ddlExpMonth default-text="Month" required="false" />
                <ddlExpYear default-text="Year" years-to-display="10" required="true" exp-date="true" exp-date-msg="Please enter a valid expiration date" required-msg="Expiration Date is required." />
              </tag>
            </tag>
            <tag name="span" class="error">
              <validationMsg for="ExpYear" />
            </tag>
          </tag> 
          <tag class="form-field col-xs-12 col-md-4 txt-baseGrey">
            <tag name="span" class="cvv-box">
              <label for="cvv" text="CVV Number" />
              <tag name="span" class="req inline_req asterisk_no">*</tag>
              <tag class="tooltipIndicator tooltip"></tag>
              <tag name="span" class="tooltiptext">For Visa®, Mastercard® and Discover® cards, the CVV is a three-digit number, and it usually appears on the back of the card, typically next to the signature box. American Express cards have four-digit CVV numbers, and they appear on the front of the card.</tag>
            </tag>
            <tag class="col-xs-12">
              <tag class="d_flex input_adjust">
                <tboxCvv class="text input-width-90" pattern="^[0-9]{3,4}$"  required-msg="CC Security Code Invalid Message" pattern-msg="CVV Number must be 3 to 4 digits only." />
              </tag>
              <tag name="span" class="error">
                <validationMsg for="Cvv" />
              </tag>
            </tag>
          </tag>
        </tag>
        
      </cardIndicatorSection>
      <additionalHtmlSection>
        <tag class="form-block flex">
          <tag class="form-field col-xs-12 col-md-4 txt-baseGrey zipCode-wid">
            <tag name="span" class="">
              <label for="postalCode" text="Zip Code" />
              <tag name="span" class="req inline_req">*</tag>
            </tag>
            <tag name="div" class="col-xs-12">
              <tag class="d_flex input_adjust">
                <textBox name="PostalCode" class="form-control" pattern="^\d{5}(-\d{4})?$" maxlength="5" required-msg="CC Zip Code Error Message" pattern-msg="Please enter the 5 digits valid zipcode." />
              </tag>
              <tag name="span" class="error">
                <validationMsg for="postalCode" />
              </tag>
            </tag>
          </tag> 
        </tag>

        
      </additionalHtmlSection>

    </htmlSection>
  </merchantHtml>

</merchantHtmlPacketModel>