<?xml version="1.0" encoding="utf-8"?>
<merchantHtmlPacketModel xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="Paymetric:XiIntercept:MerchantHtmlPacketModel">
  <iFramePacket>
      <hostUri>https://stgxm-shopping.4changeenergy.com</hostUri> 
    <cssUri>https://cms.txu.com/~/media/Themes/VistraApps/Shopping/paymetric.css?v=6.3</cssUri>
  </iFramePacket>
  <merchantHtml>
    <htmlSection class="ppsTokenizer" xmlns="Paymetric:XiIntercept:MerchantHtmlPacketModel">      
    <tag name="span" class="indicates-a-require">* Indicates a required field</tag>
      <cardIndicatorSection>

        <tag class="form-block flex flex-wrap"> 
          <tag class="form-field txt-baseGrey">
            <tag name="span" class="nickname-block flex-inline items-center">
              <label for="cardNumber" text="Número de tarjeta" />
              <tag name="span" class="req inline_req">*</tag>
              <tag class="pos_rel inline-div payment-icons">
              <cardTypeIndicator class="cardIndicator">
                <items>
                  <item for="american express" class="axIndicator" ></item>
                  <item for="mastercard" class="mcIndicator" ></item>
                  <item for="visa" class="viIndicator" ></item>
                  <item for="discover" class="diIndicator" ></item>
                </items>
              </cardTypeIndicator>
            </tag>
            </tag>
            <tag class="col-xs-12 pos_rel inline-div">
              <tboxCardNumber tokenize="true"  class="no_cursor text ppsTokenizerCard ccName tlBlock required" luhn-check="true" digits-only="true" digits-only-msg="El número de tarjeta no puede contener alfabetos o caracteres especiales" maxlength="19" maxlength-msg="El número de tarjeta debe tener entre 15 y 16 dígitos." minlength="15" minlength-msg="El número de tarjeta debe tener entre 15 y 16 dígitos."  pattern="^[0-9]{15,19}$" required-msg="Se debe ingresar el número de la tarjeta antes de efectuar un pago." pattern-msg="CC Number Error Message"  luhn-check-msg="Ingrese un número de tarjeta válido"/>
              <tag name="span" class="error">
                <validationMsg for="CardNumber" />
              </tag>
            </tag>
          </tag>
          <tag class="form-field txt-baseGrey">
            <tag name="span" class="">
              <label for="cardholderName" text="Nombre del Titular de la Tarjeta" />
              <tag name="span" class="req inline_req">*</tag>
            </tag>
            <tag class="col-xs-12 pos_rel">
              <textBox name="cardholderName" class="form-control" pattern="^[a-zA-Z]+[\s|-]?[a-zA-Z']+[\s|-]?[a-zA-Z]{1,25}$" maxlength="25" maxlength-msg="Card Holder Name must be 25 characters"  required-msg="Se debe ingresar el Nombre del Titular de la tarjeta antes de hacer un pago." pattern-msg="Invalid CardHolderName Error Message" />
              <tag name="span" class="error">
                <validationMsg for="cardholderName" />
              </tag>
            </tag>
          </tag>
        </tag>
        <tag class="form-block flex flex-wrap col-xs-12">
          <tag class="form-field col-xs-12 col-md-4 max-w-280">
            <tag name="div" class="grey_text">
              <label for="expDate" text="Fecha de expiración" />
              <tag name="span" class="req inline_req">*</tag>
            </tag>
            <tag class="item form-field month-day-selector col-md-6 col-sm-6 col-xs-12 margin_0">
              <tag name="div" class="d_flex">
                <ddlExpMonth default-text="Mes" required="false" />
                <ddlExpYear default-text="Año" years-to-display="10" required="true" exp-date="true" exp-date-msg="Por favor, introduzca una fecha de vencimiento válida" required-msg="Se debe ingresar una fecha de expiración antes de hacer un pago." />
              </tag>
            </tag>
            <tag name="span" class="error">
              <validationMsg for="ExpYear" />
            </tag>
          </tag> 
          <tag class="form-field col-xs-12 col-md-4 txt-baseGrey">
            <tag name="span" class="cvv-box">
              <label for="cvv" text="Número CVV" />
              <tag name="span" class="req inline_req asterisk_no">*</tag>
              <tag class="tooltipIndicator tooltip"></tag>
	      <tag name="span" class="tooltiptext">Para las tarjetas Visa®, Mastercard® y Discover® , el CVV (código de seguridad de la tarjeta) es un número de tres dígitos, y, generalmente, aparece en el dorso de la tarjeta, cerca del casillero de firma. Las tarjetas de American Express tienen números de CVV de cuatro dígitos y aparecen en el frente de la tarjeta.</tag>
            </tag>
            <tag class="col-xs-12">
              <tag class="d_flex input_adjust">
                <tboxCvv class="text input-width-90" pattern="^[0-9]{3,4}$"  required-msg="Número CVV requerido" pattern-msg="El número CVV debe tener entre 3 y 4 dígitos." />
              </tag>
              <tag name="span" class="error">
                <validationMsg for="Cvv" />
              </tag>
            </tag>
          </tag>
        </tag>
        
      </cardIndicatorSection>
      <additionalHtmlSection>
        <tag class="form-block flex">
          <tag class="form-field col-xs-12 col-md-4 txt-baseGrey zipCode-wid">
            <tag name="span" class="">
              <label for="postalCode" text="Código postal" />
              <tag name="span" class="req inline_req">*</tag>
            </tag>
            <tag name="div" class="col-xs-12">
              <tag class="d_flex input_adjust">
                <textBox name="PostalCode" class="form-control" pattern="^\d{5}(-\d{4})?$" maxlength="5" required-msg="Se debe ingresar un código postal antes de hacer un pago." pattern-msg="Ingrese un código postal de facturación válido de 5 dígitos." />
              </tag>
              <tag name="span" class="error">
                <validationMsg for="postalCode" />
              </tag>
            </tag>
          </tag> 
        </tag>
      </additionalHtmlSection>

    </htmlSection>
  </merchantHtml>

</merchantHtmlPacketModel>