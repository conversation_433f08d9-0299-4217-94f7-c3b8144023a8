import {
  LayoutServiceData,
  Placeholder,
  VisitorIdentification,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios from 'axios-1.4';
import { getCookie } from 'cookies-next';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import {
  clearSwapOrRenewalStatus,
  setPlanInfo,
  setSwapOrRenewalStatus,
  setTransferEligibility,
  clearTransferEligibility,
} from './stores/authUserSlice';
import { PlanInformationResponse, RenewalOffers } from './services/MyAccountAPI/types';
import Scripts from './Scripts';
import { getCAFromCookie, getEsiidFromCookie } from './utils/authTokenUtil';

interface LayoutProps {
  layoutData: LayoutServiceData;
}

const Layout = ({ layoutData }: LayoutProps): JSX.Element => {
  const isPageEditing = layoutData.sitecore.context.pageEditing;
  const mainClassPageEditing = isPageEditing ? 'editing-mode' : 'prod-mode';
  const { route } = layoutData.sitecore;
  let cid = undefined;
  let bpNumber = undefined;
  let contractaccount = undefined;
  let esiid = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;

  const router = useRouter();

  const isOopsPage = router.asPath.startsWith('/oops') || router.asPath.startsWith('/Oops');
  const authToken = getCookie('AuthToken');
  if (!isPageEditing) {
    dispatch = useAppDispatch();
    const ca = getCAFromCookie();
    if (typeof ca === 'string') contractaccount = ca;
    const esiId = getEsiidFromCookie();
    if (typeof esiId === 'string') esiid = esiId;
    cid = useAppSelector((state) => state.enrollment?.correlationid);
    bpNumber = useAppSelector((state) => state.authuser?.bpNumber);
  }

  useEffect(() => {
    const IsRenewalSwap = async (bp: string) => {
      const isRenewal = await axios.get(`/api/myaccount/renewal/createswap`, {
        params: { bpnumber: bp },
      });

      if (isRenewal && isRenewal?.data && isRenewal?.data.result !== null) {
        if (contractaccount && esiid) {
          const result = isRenewal?.data?.result?.renewalOffers?.filter((item: RenewalOffers) => {
            if (item.contractAccountNumber === contractaccount && item.esiId === esiid) {
              return item;
            } else {
              return null;
            }
          });
          if (result?.length > 0) {
            if (!isPageEditing) {
              dispatch(
                setSwapOrRenewalStatus({
                  esiid: result[0]?.esiId,
                  ca: result[0]?.contractAccountNumber,
                  renewalStatus: result[0]?.targettedSwapType,
                  promo: result[0]?.prom,
                })
              );
            }
          }
        } else {
          const esiid = isRenewal?.data?.result?.renewalOffers[0]?.esiId;
          const ca = isRenewal?.data?.result?.renewalOffers[0]?.contractAccountNumber;
          if (!isPageEditing) {
            dispatch(
              setSwapOrRenewalStatus({
                esiid: esiid,
                ca: ca,
                renewalStatus: isRenewal?.data.result?.renewalOffers[0].targettedSwapType,
                promo: isRenewal?.data.result?.renewalOffers[0].prom,
              })
            );
          }
        }
      } else {
        if (!isPageEditing) {
          dispatch(clearSwapOrRenewalStatus());
        }
      }
    };

    if (bpNumber) {
      IsRenewalSwap(bpNumber);
    }
  }, [bpNumber, contractaccount]);

  useEffect(() => {
    const fetchAndDispatchPlan = async () => {
      if (!bpNumber || !contractaccount || !esiid) {
        return;
      }

      try {
        const res = await axios.get<PlanInformationResponse>(
          `/api/myaccount/getPlanInformation?partnerNumber=${bpNumber}&accountNumber=${contractaccount}&esiid=${esiid}`
        );

        const firstProduct = res.data.result?.productDetailsList?.[0];

        if (firstProduct) {
          if (!isPageEditing) {
            dispatch(
              setPlanInfo({
                termUnit: firstProduct.termUnit,
                termMonthCount: firstProduct.termMonthCount,
              })
            );
          }
        } else {
          console.warn('[Plan Info] No productDetailsList found');
        }
      } catch (err) {
        console.error('[Plan Info] Failed to fetch plan info', err);
      }
    };

    fetchAndDispatchPlan();
  }, [bpNumber, contractaccount, esiid]);

  useEffect(() => {
    const CheckTransferEligibility = async () => {
      const reqbody = {
        BusinessPartner: bpNumber || '',
        FromESIID: esiid || '',
        ToESIID: '',
        OldProduct: '',
        IsKyp: true,
      };
      const response = await axios.post('/api/myaccount/transfer/transfereligibility', reqbody);
      if (!isPageEditing) {
        if (response.status === 200 && response.data) {
          dispatch(setTransferEligibility(response.data.result === '' ? false : true));
        } else {
          dispatch(clearTransferEligibility());
        }
      }
    };

    if (bpNumber && esiid) {
      try {
        CheckTransferEligibility();
      } catch (error) {
        if (!isPageEditing) {
          dispatch(clearTransferEligibility());
        }
      }
    } else {
      if (!isPageEditing) {
        dispatch(clearTransferEligibility());
      }
    }
  }, [esiid, bpNumber]);

  return (
    <>
      <Scripts />
      <Head>
        <title>{route?.fields?.pageTitle?.value || 'Page'}</title>
        {/* Removed static favicon and added through Dynamic HTML */}
      </Head>
      {/*
        VisitorIdentification is necessary for Sitecore Analytics to determine if the visitor is a robot.
        If Sitecore XP (with xConnect/xDB) is used, this is required or else analytics will not be collected for the JSS app.
        For XM (CMS-only) apps, this should be removed.

        VI detection only runs once for a given analytics ID, so this is not a recurring operation once cookies are established.
      */}
      <VisitorIdentification />

      {!isPageEditing ? (
        <>
          {/* root placeholder for the app, which we add components to using route data */}
          {route && (
            <>
              <Placeholder name="jss-bodytop" rendering={route} />
              {isOopsPage ? (
                <Placeholder name="jss-main" rendering={route} />
              ) : (
                <main
                  id="maincontent"
                  className={`${
                    authToken === undefined
                      ? ''
                      : 'flex flex-col md:flex-row ipad:flex-col wide:flex-col max-w-[1600px] m-auto relative sm:min-h-[100vh]'
                  }`}
                >
                  <aside className="flex-col md:flex md:flex-row sm:max-w-[320px] ipad:max-w-full wide:max-w-full w-full border-r-2 border-none border-borderQuattuordenary ipad:w-full  wide:w-full justify-center pb-0 sm:absolute sm:top-[44px] left-0 sm:left-[30px] sm:rounded sm:overflow-hidden block ipad:relative wide:relative wide:left-0 wide:top-0 ipad:left-0 ipad:top-0 ipad:pb-0 wide:pb-0 shadow-3xl bg-bgQuinary">
                    <div
                      className={`pb-0 sm:bg-bgQuinary ${authToken === undefined ? 'hidden' : ''}`}
                    >
                      <Placeholder name="jss-left-nav" rendering={route} />
                    </div>
                  </aside>
                  <div
                    tabIndex={-1}
                    className="flex-grow md:w-full flex flex-col w-full sm:min-h-[800px] sm:min-w-[800px] ipad:min-w-full wide:min-w-full"
                  >
                    <Placeholder name="jss-main" rendering={route} />
                  </div>
                </main>
              )}
              <Placeholder name="jss-bodybottom" rendering={route} />
            </>
          )}
        </>
      ) : (
        <div className={mainClassPageEditing}>
          {route && <Placeholder name="jss-bodytop" rendering={route} />}
          {route && <Placeholder name="jss-main" rendering={route} />}
          {route && <Placeholder name="jss-enrollmentconfirmation" rendering={route} />}
          {route && <Placeholder name="jss-myaccount-viewplans" rendering={route} />}
          {route && <Placeholder name="jss-left-nav" rendering={route} />}
          {route && <Placeholder name="jss-bodybottom" rendering={route} />}
          {route && <Placeholder name="jss-orderinfo" rendering={route} />}
          {route && <Placeholder name="jss-rightside-section" rendering={route} />}
          {route && <Placeholder name="jss-leftnav-container" rendering={route} />}
          {route && <Placeholder name="jss-renewal" rendering={route} />}
          {route && <Placeholder name="jss-renewal-changeaddress" rendering={route} />}
          {route && <Placeholder name="jss-transfer-orderinfo" rendering={route} />}
          {route && <Placeholder name="jss-commoncontainerwithoutsidemenu" rendering={route} />}
          {route && <Placeholder name="jss-viewplans" rendering={route} />}
          {route && <Placeholder name="jss-rightside-section" rendering={route} />}
          {route && <Placeholder name="jss-popup" rendering={route} />}
          {route && <Placeholder name="jss-miniconfirmation" rendering={route} />}
          {route && <Placeholder name="jss-welcomeblock-details" rendering={route} />}
          {route && <Placeholder name="jss-add-orderinfo" rendering={route} />}
          {route && <Placeholder name="jss-placeorder" rendering={route} />}
          {route && <Placeholder name="jss-addonplans" rendering={route} />}
          {route && <Placeholder name="jss-renewal-changeaddress" rendering={route} />}
          {route && <Placeholder name="jss-transfer-serviceinfo" rendering={route} />}
          {route && <Placeholder name="jss-renewal-orderinfo" rendering={route} />}
        </div>
      )}
      <p className="hidden">CID:{cid}</p>
    </>
  );
};

export default Layout;
