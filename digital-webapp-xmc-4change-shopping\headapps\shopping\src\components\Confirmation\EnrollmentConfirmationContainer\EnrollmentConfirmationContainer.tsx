import { Placeholder, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';

const EnrollmentConfirmationContainer = (props: ComponentProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  return (
    <div className="w-full flex flex-col mb-16 basis-9/12 ml-0 wide:ml-0 ipad:ml-0 ipad:pl-6">
      <Placeholder
        rendering={props.rendering}
        name="jss-enrollmentconfirmation"
        render={(components) => {
          return <div className="flex flex-col items-center">{components}</div>;
        }}
      />
    </div>
  );
};

export default aiLogger(EnrollmentConfirmationContainer, EnrollmentConfirmationContainer.name);
