import { CloseButton } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';
import { RichText, Text } from '@sitecore-jss/sitecore-jss-nextjs';
import Button from 'components/Elements/Button/Button';
import { useRouter } from 'next/router';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';

interface ExitEnrollmentProps {
  heading: string;
  description: string;
  _tmp_vst_description: string;
  _tmp_rv_description: string;
  fuseEnabled: boolean;
  continueText: string;
  exitText: string;
  exitEnrollment: () => void;
}

const ExitEnrollment = ({
  context,
  id,
  innerProps,
}: ContextModalProps<ExitEnrollmentProps>): JSX.Element => {
  const router = useRouter();
  const { utm_source } = router.query as QueryParamsMapType;
  let description = innerProps.description;
  if (innerProps.fuseEnabled && utm_source === 'rv_organic') {
    description = innerProps._tmp_rv_description;
  } else if (innerProps.fuseEnabled && utm_source !== 'rv_organic') {
    description = innerProps._tmp_vst_description;
  }
  return (
    <>
      <CloseButton
        iconSize={23}
        className="ml-auto md:mr-2 md:mt-2 text-textQuattuordenary "
        onClick={() => context.closeModal(id)}
      />
      <div className="flex flex-col gap-5 mb-2 md:ml-[80px]  md:mt-4 md:mb-12">
        <Text
          tag="p"
          className="font-primaryBlack text-[20px] leading-[26px] -tracking-[0.25px] w-[245px] md:text-plus3 md:-tracking-[0.5px] md:w-[488px] "
          field={{ value: innerProps.heading }}
        />
        <RichText
          tag="p"
          className="inline-link font-primaryRegular text-minus1 w-[245px] text-textQuattuordenary md:text-base md:w-[554px]"
          field={{ value: description }}
        />
        <div className="flex flex-col gap-5 mt-3 items-center md:flex-row md:justify-start">
          <Button className="w-[272px]" onClick={() => context.closeModal(id)}>
            {innerProps.continueText}
          </Button>
          <Button className="w-[272px]" variant="secondary" onClick={innerProps.exitEnrollment}>
            {innerProps.exitText}
          </Button>
        </div>
      </div>
    </>
  );
};

export default ExitEnrollment;
