import { UnstyledButton } from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import {
  Field,
  LinkField,
  Placeholder,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios, { AxiosError, AxiosResponse } from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { TokenData, Tokens } from 'components/MyAccount/Login/Login';
import { PaymetricResponse } from 'components/PaymetricIntegration/PaymetricIntegration';
import { setCookie } from 'cookies-next';
import jwt from 'jsonwebtoken';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowRight } from '@fortawesome/pro-light-svg-icons';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useNavigationDirection } from 'src/hooks/useNavigationDirection';
import {
  OrderAddonProductRequest,
  OrderAddonProductResponse,
  OrderedAddonProduct,
} from 'src/services/AddOnProductAPI/types';
import {
  setAutoPay,
  setCoaSuccess,
  setCustomerUserName,
  setMyAccountInfo,
  setSelectedCharityInfo,
  setShowMyAccountBtn,
} from 'src/stores/enrollmentSlice';
import { clearNCPProducts, setOrderedPlans } from 'src/stores/planSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import * as CONSTANTS from 'src/utils/constants';
import { DwellingType } from 'src/utils/query-params-mapping';
import { z } from 'zod';
import { SecurityQuestionTypes } from '../CreateAccount/CreateAccount';
import handleSubmit from 'src/utils/placeOrderSubmit';
import { faChevronDoubleRight } from '@fortawesome/pro-solid-svg-icons';
import { CommPrefResponse, CommPrefBody } from 'src/services/EnrollmentAPI/types';
import { setJWTAuthToken } from 'src/utils/authTokenUtil';
import { useLoader } from 'src/hooks/modalhooks';
import { InitailCharityValue } from 'components/common/CharitySelection/componentprops/CharitySelection.types';

export enum EnrollmentChannel {
  Txu = 1,
  ELS = 2,
  MPP = 3,
}

export enum PortalType {
  Unknown = 0,
  CSPMMF = 1,
  Installer = 2,
  MarketingPartner = 3,
  Residential = 4,
  SMBLCI = 5,
  SocialAgency = 6,
  Prepaid = 7,
}

export type COAFormValues = {
  password: string;
  custUserName: string;
  confirmpassword: string;
  securityanswer: string;
  securityquestion: string;
  acceptance: boolean;
  paperlessBilling: boolean;
  accountholdername: string;
  routingnumber: string;
  bankaccountnumber: string;
  billingzipcode: string;
  agreeTerms: boolean;
  isAutoPay: boolean;
  paymentmethod: string;
  isESaverPlan: boolean;
  isEVPlan: boolean;
};

interface MiniConfirmationContainerTEEProps extends ComponentProps {
  fields: {
    SecurityQuestions: SecurityQuestionTypes[];
    SubmitButtonLabel: Field<string>;
    SkipLabel: Field<string>;
    ConfirmationPageLink: LinkField;
    UserNameError: Field<string>;
    securityQuestionError: Field<string>;
    PasswordEmptyError: Field<string>;
    PasswordRegxError: Field<string>;
    ConfirmPasswordNotMatchingError: Field<string>;
    AcceptanceError: Field<string>;
    ConfirmPasswordEmptyError: Field<string>;
    SecurityAnswerErrorMessage: Field<string>;
    UserNameRegsError: Field<string>;
    UserNameRegexPattern: Field<string>;
    PasswordRegexPattern: Field<string>;
  };
}

const MiniConfirmationContainerTEE = (props: MiniConfirmationContainerTEEProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let dispatch: ReturnType<typeof useAppDispatch>;
  let storeData = undefined;
  let planData = undefined;
  if (!isPageEditing) {
    storeData = useAppSelector((state) => state.enrollment);
    planData = useAppSelector((state) => state.plans);
    dispatch = useAppDispatch();
  }
  const [accessToken, setAccessToken] = useState<string>('');
  const router = useRouter();
  useNavigationDirection();
  const { customerclassification } = router.query;
  const { dwel } = router.query;
  const [isAutoPayCall, setAutoPayCall] = useState(true);
  const [isAutoPayChecked, setIsAutoPayChecked] = useState(false);
  const [containerSelectedCharity, setContainerSelectedCharity] = useState<InitailCharityValue>();
  const regexUsername = props.fields?.UserNameRegexPattern?.value?.slice(1, -1) || '';
  const regexPassword = props.fields?.PasswordRegexPattern?.value?.slice(1, -1) || '';
  const { openModal, closeAllModal } = useLoader();

  const coaSchema = z.object({
    custUserName: z
      .string()
      .nonempty({ message: props.fields?.UserNameError.value })
      .regex(new RegExp(regexUsername), {
        message: props.fields?.UserNameRegsError.value,
      }),
    securityquestion: z.string().nonempty({ message: props.fields?.securityQuestionError.value }),
    securityanswer: z
      .string()
      .nonempty({ message: props.fields?.SecurityAnswerErrorMessage.value }),
    password: z
      .string()
      // .min(5, { message: props.fields?.PasswordRegxError })
      .nonempty({ message: props.fields?.PasswordEmptyError.value })
      .regex(new RegExp(regexPassword), {
        message: props.fields?.PasswordRegxError.value,
      }),
    confirmpassword: z
      .string()
      .nonempty({ message: props.fields?.ConfirmPasswordEmptyError.value }),
    acceptance: z.literal(true, {
      errorMap: () => ({ message: props.fields?.AcceptanceError.value }),
    }),
  });

  const remschema = z.object({
    paperlessBilling: z.boolean().optional(),
    isEVPlan: z.boolean().optional(),
    accountholdername: z.string().superRefine((arg, ctx) => {
      const isValid = isAutoPay && paymentmethod === 'bank';
      const pattern = /^[a-zA-Z ]{1,30}$/;
      if (isValid && arg === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter the account holder name',
        });
      } else if (arg !== '' && !pattern.test(arg)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter the valid account holder name ',
        });
      }
    }),
    routingnumber: z.string().superRefine((arg, ctx) => {
      const isValid = isAutoPay && paymentmethod === 'bank';
      const pattern = /^[0-9]{9,17}$/;
      if (isValid && arg === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter a routing number',
        });
      } else if (arg !== '' && !pattern.test(arg)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter a valid routing number',
        });
      }
    }),
    bankaccountnumber: z.string().superRefine((arg, ctx) => {
      const isValid = isAutoPay && paymentmethod === 'bank';
      const pattern = /^[0-9]*$/;
      if (isValid && arg === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter an account number',
        });
      } else if (arg !== '' && !pattern.test(arg)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter an valid account number',
        });
      }
    }),
    billingzipcode: z.string().superRefine((arg, ctx) => {
      const isValid = isAutoPay && paymentmethod === 'bank';
      const pattern = /^[0-9]{5}(?:-[0-9]{4})?$/;
      if (isValid && arg === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter a zip code',
        });
      } else if (arg !== '' && !pattern.test(arg)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter a valid zip code',
        });
      }
    }),
  });

  const schema = isAutoPayChecked
    ? remschema.merge(coaSchema).refine((data) => data.password === data.confirmpassword, {
        path: ['confirmpassword'],
        message: props.fields?.ConfirmPasswordNotMatchingError.value,
      })
    : remschema;

  const form = useForm<COAFormValues>({
    initialValues: {
      password: '',
      confirmpassword: '',
      securityanswer: '',
      securityquestion: props?.fields?.SecurityQuestions[0]?.fields?.Title?.value,
      acceptance: false,
      paperlessBilling: false,
      //autopay
      accountholdername: '',
      routingnumber: '',
      billingzipcode: '',
      bankaccountnumber: '',
      // cardnumber: '',
      // cardholdername: '',
      // expirymonth: '',
      // expiryyear: '',
      // cvv: '',
      // zipcode: '',
      isAutoPay: false,
      agreeTerms: false,
      paymentmethod: 'bank',
      isESaverPlan: false,
      // custEmailAddress: '',
      custUserName: '',
      isEVPlan: false,
    },
    validate: zodResolver(schema),
    validateInputOnChange: [
      'password',
      'confirmpassword',
      'securityanswer',
      'securityquestion',
      'acceptance',
      'paperlessBilling',
      'accountholdername',
      'routingnumber',
      'billingzipcode',
      'bankaccountnumber',
      'isAutoPay',
      'agreeTerms',
      'paymentmethod',
      'isESaverPlan',
      'isEVPlan: false',
    ],
    validateInputOnBlur: true,
  });
  const isAutoPay = form.values.isAutoPay;
  const paymentmethod = form.values.paymentmethod;

  useEffect(() => {
    if (form.values.isAutoPay) setIsAutoPayChecked(false);
    else setIsAutoPayChecked(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  });

  function addLeadingZeros(input: string) {
    const eightDigitNumber = input;
    if (eightDigitNumber.length === 8) {
      return `00${eightDigitNumber}`;
    } else {
      return eightDigitNumber;
    }
  }

  const createOnlineAccount = async (values: COAFormValues) => {
    try {
      const data = {
        // Username: form.values.custEmailAddress, //isEmailIdValid ? form.values.custEmailAddress : storeData.customerInfo.email,
        Username: form.values.custUserName,
        Email: form.values.custUserName, //isEmailIdValid ? form.values.custEmailAddress : storeData.customerInfo.email,
        SecurityQuestion: values.securityquestion,
        SecurityAnswer: values.securityanswer,
        CustomerClassification: customerclassification,
        EnrollmentChannel: EnrollmentChannel.Txu,
        FirstName: storeData.customerInfo.firstName,
        LastName: storeData.customerInfo.lastName,
        LanguagePreference:
          storeData.customerInfo.correspondanceLanguage === 'Español'
            ? 'Spanish'
            : storeData.customerInfo.correspondanceLanguage,
        OrganizationBPNumber: null,
        Password: values.password,
        PaperlessBilling: storeData.isPaperlessBilling, //form.values.paperlessBilling,
        SpecialOffers: false,
        PersonBPNumber: addLeadingZeros(storeData.enrollmentInfo.bpNumber),
        SendEmailInvitation: values.acceptance,
        isBusinessUser: false,
        AccountNumber: storeData.enrollmentInfo.contractAccountNumber,
        Portal: PortalType.Residential,
        isEnrollment: true,
      };
      const response = await axios.post('/api/customer/account', data);
      dispatch(setCustomerUserName(data.Username));
      //dispatch(setPaperLessBilling(form.values.paperlessBilling));
      dispatch(setCoaSuccess(true));
      if (response.data.result.result != undefined) {
        dispatch(setShowMyAccountBtn(true));
      } else {
        dispatch(setShowMyAccountBtn(false));
      }
      const error = response.data.result.result ? '' : response.data.result.messages[0];
      if (error) {
        dispatch(setShowMyAccountBtn(false));
        closeAllModal();
      }
      // form.setFieldError('custEmailAddress', error);
      form.setFieldError('custUserName', error);
      return response.data.result.result;
    } catch (error) {
      const err = error as AxiosError;
      logErrorToAppInsights(err, {
        componentStack: 'MiniConfirmationContainerTEE-createOnlineAccount',
      });
      console.log(err.response?.data);
      closeAllModal();
    }
  };

  const checkUsername = async () => {
    try {
      const response = await axios.get(`/api/usercheck?username=${form.values.custUserName}`);
      const value = response.data.result ? '' : form.values.custUserName;
      form.setFieldValue('custUserName', value);
      const errorMsg = value === '' ? 'User name already exist please use different user name' : '';
      form.setFieldError('custUserName', errorMsg);
      return response.data.result;
    } catch (error) {
      const err = error as AxiosError;
      logErrorToAppInsights(err, {
        componentStack: 'MiniConfirmationContainerTEE-createOnlineAccount',
      });
      console.log(err.response?.data);
    }
  };

  const cookieDomain = process.env.NEXT_PUBLIC_COOKIES_OPTIONS_DOMAIN;

  const setTokensInCookie = (
    tokens: Tokens,
    customer_classification: string,
    portalType: string,
    customer_name: string
  ) => {
    const combinedTokenString = JSON.stringify(tokens);
    setJWTAuthToken(combinedTokenString, tokens.expires_in, cookieDomain);
    setCookie('customer_classification', customer_classification, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain,
      maxAge: tokens.expires_in,
    });
    setCookie('PortalType', portalType, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain,
    });
    setCookie('customer_name', customer_name, {
      path: '/',
      sameSite: 'none',
      secure: true,
      domain: cookieDomain,
    });
  };

  //get access token for my account page
  const getMyAccountAccessToken = async () => {
    try {
      const data = {
        username: form.values.custUserName,
        password: form.values.password,
      };
      const response = await axios.post('/api/myaccount/lplogin', data);
      dispatch(setMyAccountInfo(response.data));
      const decodedToken = jwt.decode(response.data.access_token) as TokenData;
      const portalType =
        CONSTANTS.PortalType[decodedToken.portal_type as keyof typeof CONSTANTS.PortalType];
      const customer_name = storeData.customerInfo.firstName;
      const tokens: Tokens = {
        access_token: response.data.access_token,
        refresh_token: response.data.refresh_token,
        expires_in: response.data.expires_in,
      };
      setTokensInCookie(tokens, decodedToken.customer_classification, portalType, customer_name);
    } catch (error) {
      const err = error as AxiosError;
      logErrorToAppInsights(err, {
        componentStack: 'MiniConfirmationContainerTEE-getMyAccountAccessToken',
      });
      console.log(err.response?.data);
    }
  };

  const orderAddonProducts = async () => {
    try {
      const addonPlanKeys = Object.keys(planData.addonPlans);
      if (addonPlanKeys && addonPlanKeys.length !== 0) {
        const selectedAddons: OrderedAddonProduct[] = addonPlanKeys.map((key) => {
          return {
            productId: planData.addonPlans[key].planId,
            quantity: 1,
          };
        });

        const orderAddonReq = await axios.post<
          OrderAddonProductResponse,
          AxiosResponse<OrderAddonProductResponse>,
          OrderAddonProductRequest
        >(
          '/api/plans/addonplans',
          {
            businessPartnerNumber: storeData.enrollmentInfo.bpNumber,
            contractAccountNumber: storeData.enrollmentInfo.contractAccountNumber,
            esiid: storeData.serviceInfo.esiid,
            zipCode: storeData.serviceInfo.postalCode,
            currentPlanId: planData.selectedPlan.planId,
            dwellingType: DwellingType[dwel as string],
            language:
              storeData.customerInfo.correspondanceLanguage === 'Español'
                ? 'Spanish'
                : storeData.customerInfo.correspondanceLanguage,
            productOrders: selectedAddons,
          },
          { headers: { 'Content-Type': 'application/json' } }
        );

        console.log(orderAddonReq);

        dispatch(setOrderedPlans(orderAddonReq.data.result));
      }
    } catch (err) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'MiniConfirmationContainerTEE-orderAddonProducts',
      });
      console.log(error);
    }
  };

  const orderNonCommodityProducts = async () => {
    try {
      const ncpPlanKeys = Object.keys(planData.NonCommodityProducts);
      if (ncpPlanKeys && ncpPlanKeys.length !== 0) {
        const selectedAddons: OrderedAddonProduct[] = ncpPlanKeys.map((key) => {
          return {
            productId: planData.NonCommodityProducts[key].name,
            quantity: 1,
          };
        });

        const orderAddonReq = await axios.post<
          OrderAddonProductResponse,
          AxiosResponse<OrderAddonProductResponse>,
          OrderAddonProductRequest
        >(
          '/api/plans/addonplans',
          {
            businessPartnerNumber: storeData.enrollmentInfo.bpNumber,
            contractAccountNumber: storeData.enrollmentInfo.contractAccountNumber,
            esiid: storeData.serviceInfo.esiid,
            zipCode: storeData.serviceInfo.postalCode,
            currentPlanId: planData.selectedPlan.planId,
            dwellingType: DwellingType[dwel as string],
            language:
              storeData.customerInfo.correspondanceLanguage === 'Español'
                ? 'Spanish'
                : storeData.customerInfo.correspondanceLanguage,
            productOrders: selectedAddons,
          },
          { headers: { 'Content-Type': 'application/json' } }
        );

        console.log(orderAddonReq);

        dispatch(setOrderedPlans(orderAddonReq.data.result));
      }
    } catch (err) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'MiniConfirmationContainerTEE-orderAddonProducts',
      });
      console.log(error);
    }
  };

  async function redirectToNextPage() {
    setTimeout(setCommPref, 3000);
    router.push({
      pathname: props.fields.ConfirmationPageLink.value.href,
      query: { ...router.query },
    });
  }

  const setCommPref = async () => {
    const prefData = {
      Partner: storeData.enrollmentInfo.bpNumber,
      Email: storeData.customerInfo.email,
      isPaperlessBilling: storeData.isPaperlessBilling,
    };
    const setCommPrefReq = await axios.post<
      CommPrefResponse,
      AxiosResponse<CommPrefResponse>,
      CommPrefBody
    >('/api/customer/setcommpref', prefData, { headers: { 'Content-Type': 'application/json' } });
    console.log(setCommPrefReq);
  };

  // const getDaysInMonth = () => {
  //   return new Date().toISOString();
  // };
  const autoPayCard = async (resPacket: PaymetricResponse): Promise<void> => {
    try {
      const data = {
        ContractAccountNumber: storeData.enrollmentInfo.contractAccountNumber,
        HoldersName: resPacket.message.Fields.FormField[4].Value,
        AccountId: resPacket.message.Fields.FormField[3].Value,
        DisplayAccountNumber: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
        ProfileId: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
        PartnerNumber: storeData.enrollmentInfo.bpNumber,
        routingNumber: '',
        Expiration: new Date(
          parseInt(resPacket.message.Fields.FormField[2].Value),
          parseInt(resPacket.message.Fields.FormField[1].Value),
          0
        ).toISOString(),
        CVV: `${resPacket.message.Fields.FormField[5].Value}`,
        mode: paymentmethod,
      };
      const response = await axios.post('/api/payments/paymetric/autopay', data);
      if (response.data?.result && response.data?.result.indicator === 'Success') {
        dispatch(setAutoPay(true));
      }
      console.log('Autopay', response);
      setAutoPayCall(false);
    } catch (error) {
      const err = error as AxiosError;
      logErrorToAppInsights(err, {
        componentStack: 'MiniConfirmationContainerTEE-autoPay-card',
      });
      console.log(err.response?.data);
    }
  };

  const autoPayBank = async () => {
    try {
      const data = {
        ContractAccountNumber: storeData.enrollmentInfo.contractAccountNumber,
        HoldersName: form.values.accountholdername,
        AccountId: form.values.bankaccountnumber,
        DisplayAccountNumber: form.values.bankaccountnumber.substr(
          form.values.bankaccountnumber.length - 4
        ),
        //ProfileId: form.values.bankaccountnumber.substr(form.values.bankaccountnumber.length - 4),
        PartnerNumber: storeData.enrollmentInfo.bpNumber,
        routingNumber: form.values.routingnumber,
        //Expiration: getDaysInMonth(),
        CVV: '',
        mode: paymentmethod,
      };
      const response = await axios.post('/api/payments/paymetric/autopay', data);
      if (response.data?.result && response.data?.result.indicator === 'Success') {
        dispatch(setAutoPay(true));
      }
      console.log('Autopay', response);
      setAutoPayCall(false);
    } catch (error) {
      const err = error as AxiosError;
      logErrorToAppInsights(err, {
        componentStack: 'MiniConfirmationContainerTEE-autoPay-bank',
      });
      console.log(err.response?.data);
    }
  };

  const handleFormSubmit = async (values: COAFormValues) => {
    await CharityApiCall();
    orderAddonProducts();
    orderNonCommodityProducts();
    if (isAutoPay && isAutoPayCall) {
      if (paymentmethod === 'bank') {
        autoPayBank();
      } else {
        handleSubmit(autoPayCard, accessToken);
      }
    }

    if (values.custUserName !== '') {
      const isValid = await checkUsername();
      if (!isValid) {
        openModal();
        const redirectToNext = await createOnlineAccount(values);
        if (redirectToNext) {
          getMyAccountAccessToken();
          redirectToNextPage();
        } else {
          closeAllModal();
        }
      } else {
        closeAllModal();
      }
    } else {
      redirectToNextPage();
    }
  };

  const SkipFunction = async () => {
    openModal();
    await CharityApiCall();
    dispatch(setShowMyAccountBtn(false));
    dispatch(clearNCPProducts());
    redirectToNextPage();
  };

  const CharityApiCall = async () => {
    if (containerSelectedCharity?.id) {
      const saveresponse = await axios.post('/api/charity/savecharitycode', {
        ContractAccount: storeData.enrollmentInfo?.contractAccountNumber,
        Charity: containerSelectedCharity?.code,
      });
      if (saveresponse?.data?.result?.indicator === 'Success') {
        dispatch(setSelectedCharityInfo(containerSelectedCharity));
      }
    }
  };
  return (
    <div className="w-full flex flex-col justify-center items-center">
      <form className="w-full" onSubmit={form.onSubmit((values) => handleFormSubmit(values))}>
        <Placeholder
          rendering={props.rendering}
          name="jss-miniconfirmation"
          form={form}
          accessToken={accessToken}
          setAccessToken={setAccessToken}
          setContainerSelectedCharity={setContainerSelectedCharity}
          render={(components) => {
            return (
              <div className="flex flex-col w-full mb-[60px] sm:mb-[80px]">
                {components.map((component, index) => {
                  return (
                    <div
                      key={index}
                      className={`flex flex-col w-full items-center ${
                        index >= 2 ? 'px-[15px]' : ''
                      }`}
                    >
                      {component}
                    </div>
                  );
                })}
                <div className="flex flex-col md:flex-row gap-5 md:gap-8 items-center sm:w-[798px] w-full sm:max-w-[894px] px-4 sm:self-center sm:my-5 mt-2 wide:w-full ipad:w-full ipad:px-[40px] wide:justify-center ipad:justify-center">
                  <Button
                    className=" mt-[15px] sm:mt-[5px] rvFormSubmitCtnuBtn font-primaryBold flex pt-[10px] w-[250px]"
                    showLoader={false}
                  >
                    {props.fields.SubmitButtonLabel.value}{' '}
                    <FontAwesomeIcon
                      className=" text-minus3 relative top-[1px] hidden"
                      icon={faChevronDoubleRight}
                    />
                  </Button>
                  <UnstyledButton
                    type="button"
                    className="text-textPrimary hover:text-textSecondary text-[18px] decoration-textPrimary underline-offset-4 font-primaryBold cursor-pointer flex flex-row gap-2 items-center text-minus1"
                    onClick={SkipFunction}
                  >
                    {props.fields.SkipLabel.value}
                    <FontAwesomeIcon className="mt-2" icon={faArrowRight} />
                  </UnstyledButton>
                </div>
              </div>
            );
          }}
        />
      </form>
    </div>
  );
};

export default aiLogger(MiniConfirmationContainerTEE, MiniConfirmationContainerTEE.name);
