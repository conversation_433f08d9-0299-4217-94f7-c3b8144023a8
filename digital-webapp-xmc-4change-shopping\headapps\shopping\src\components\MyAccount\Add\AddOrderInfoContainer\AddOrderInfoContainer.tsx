import { useForm, zodResolver } from '@mantine/form';
import {
  Field,
  LinkField,
  Placeholder,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { AxiosError } from 'axios';
import axios, { AxiosResponse } from 'axios-1.4';
import { SelectedItemType } from 'components/AddressTypeAhead/AddressTypeAhead';
import Button from 'components/Elements/Button/Button';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import {
  ConnectResponse,
  ConnectValidateResponse,
  CreateContractAccountResponse,
} from 'src/services/EnrollmentAPI/types';
import {
  PaperlessBillingResponse,
  UpdateAddBillingAddressResponse,
} from 'src/services/MyAccountAPI/types';
import { setAddHomeInfo } from 'src/stores/addSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { DwellingType, QueryParamsMapType } from 'src/utils/query-params-mapping';
import { z } from 'zod';

type AddOrderInfoContainerProps = ComponentProps & {
  fields: {
    ButtonText: Field<string>;
    RedirectionLink: LinkField;
    ActiveESIIDRedirectionLink: LinkField;
    MovingInOwnPremiseLink: LinkField;
    SwitchHoldLink: LinkField;
    BillingStreetNumberError: Field<string>;
    BillingStreetNameError: Field<string>;
    BillingUnitNumberError: Field<string>;
    BillingCityError: Field<string>;
    BillingZipcodeError: Field<string>;
    BillingStateError: Field<string>;
    BillingPOBoxError: Field<string>;
    BillingPOBoxCityError: Field<string>;
    BillingPOBoxStateError: Field<string>;
    BillingPOBoxZipcodeError: Field<string>;
    StartServiceDateValidationError: Field<string>;
    ContractAccountError: Field<string>;
    EmailAddressError: Field<string>;
    FirstNameError: Field<string>;
    LastNameError: Field<string>;
  };
};

export interface AddHomeNextReqBody {
  isExistingBill: boolean;
  bpNumber: string;
  city: string;
  streetNumber: string;
  unit: string;
  postalCode: string;
  streetName: string;
  state: string;
  country: string;
  language: string;
  contractAccountNumber: string;
  startDate: string;
  esiid: string;
  customerIntent: string;
  dwellingType: string;
  productId: string;
  incentiveId: string;
  promoCode: string;
  campaignId: string;
  channel: string;
  vendorId: string;
  billingOption: 'sameAddress' | 'differentAddress' | 'poBox';
  billingStreetNumber: string;
  billingStreetAddress: string;
  billingAptOrUnit: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  poBox: string;
  poBoxCity: string;
  poBoxState: string;
  poBoxZipCode: string;
  isPaperlessBilling: boolean;
  paperlessEmail: string;
  secondaryAccountFirstName: string;
  secondaryAccountLastName: string;
  enrollDate: string;
}

interface AddHomeNextResponse {
  createContractResponse: CreateContractAccountResponse | null;
  connectValidateResponse: ConnectValidateResponse;
  connectResponse: ConnectResponse;
  billingAddressResponse: UpdateAddBillingAddressResponse;
  paperlessBillingResponse: PaperlessBillingResponse;
}

export interface AddOrderInfoFormType extends SelectedItemType {
  startdate: string;
  isExistingBill: 'true' | 'false';
  contractAccount: string;
  billingOption: 'sameAddress' | 'differentAddress' | 'poBox';
  isSecondaryAccountHolder: boolean;
  isPaperlessBilling: boolean;
  billingStreetNumber: string;
  billingStreetAddress: string;
  billingAptOrUnit: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  poBox: string;
  poBoxCity: string;
  poBoxState: string;
  poBoxZipCode: string;
  secondaryAccountFirstName: string;
  secondaryAccountLastName: string;
  paperlessBillingEmail: string;
  // isRenewableEnergy: boolean;
}

const AddOrderInfoContainer = (props: AddOrderInfoContainerProps): JSX.Element => {
  let dispatch: ReturnType<typeof useAppDispatch>;
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let selectedAddress = undefined;
  let bpNumber = '';
  let selectedPlan = '';
  if (!isPageEditing) {
    dispatch = useAppDispatch();
    selectedAddress = useAppSelector((state) => state.add?.selectedAddress);
    bpNumber = useAppSelector((state) => state.authuser?.bpNumber);
    selectedPlan = useAppSelector((state) => state.plans?.selectedPlan);
  }

  const router = useRouter();
  const { dwel, cint, prom } = router.query as QueryParamsMapType;
  const { openModal } = useLoader();
  //#region Schema
  const serviceInformationSchema = z.object({
    esiid: z.string().nonempty({ message: 'Service address required' }),
    startdate: z.string().nonempty({ message: props.fields.StartServiceDateValidationError.value }),
  });

  const accountSetupSchema = z.discriminatedUnion('isExistingBill', [
    z.object({
      isExistingBill: z.literal('true'),
      contractAccount: z.string().nonempty({ message: props.fields.ContractAccountError.value }),
    }),
    z.object({
      isExistingBill: z.literal('false'),
    }),
  ]);

  const billingInfoSchema = z.discriminatedUnion('billingOption', [
    z.object({
      billingOption: z.literal('sameAddress'),
    }),
    z.object({
      billingOption: z.literal('differentAddress'),
      billingStreetNumber: z
        .string()
        .nonempty({ message: props.fields.BillingStreetNumberError.value }),
      billingStreetAddress: z
        .string()
        .nonempty({ message: props.fields.BillingStreetNameError.value }),
      billingAptOrUnit: z.string().nonempty({ message: props.fields.BillingUnitNumberError.value }),
      billingCity: z.string().nonempty({ message: props.fields.BillingCityError.value }),
      billingState: z.string().nonempty({ message: props.fields.BillingStateError.value }),
      billingZipCode: z.string().nonempty({ message: props.fields.BillingZipcodeError.value }),
    }),
    z.object({
      billingOption: z.literal('poBox'),
      poBox: z.string().nonempty({ message: props.fields.BillingPOBoxError.value }),
      poBoxCity: z.string().nonempty({ message: props.fields.BillingPOBoxCityError.value }),
      poBoxState: z.string().nonempty({ message: props.fields.BillingPOBoxStateError.value }),
      poBoxZipCode: z.string().nonempty({ message: props.fields.BillingPOBoxZipcodeError.value }),
    }),
  ]);

  const paperlessBillingSchema = z.discriminatedUnion('isPaperlessBilling', [
    z.object({
      isPaperlessBilling: z.literal(true),
      paperlessBillingEmail: z.string().email({ message: props.fields.EmailAddressError.value }),
    }),
    z.object({
      isPaperlessBilling: z.literal(false),
    }),
  ]);

  const secondaryAccountHolderSchema = z.discriminatedUnion('isSecondaryAccountHolder', [
    z.object({
      isSecondaryAccountHolder: z.literal(true),
      secondaryAccountFirstName: z
        .string()
        .nonempty({ message: props.fields.FirstNameError.value }),
      secondaryAccountLastName: z.string().nonempty({ message: props.fields.LastNameError.value }),
    }),
    z.object({
      isSecondaryAccountHolder: z.literal(false),
    }),
  ]);

  const finalSchema = serviceInformationSchema
    .and(accountSetupSchema)
    .and(billingInfoSchema)
    .and(paperlessBillingSchema)
    .and(secondaryAccountHolderSchema);
  //#region

  const form = useForm<AddOrderInfoFormType>({
    initialValues: {
      startdate: '',
      value: selectedAddress?.value,
      label: selectedAddress?.label,
      esiid: selectedAddress?.esiid,
      city: selectedAddress?.city,
      state: selectedAddress?.state,
      zip: selectedAddress?.zip,
      street: selectedAddress?.street,
      house_nbr: selectedAddress?.house_nbr,
      tdsp: selectedAddress?.tdsp,
      isExistingBill: 'false',
      contractAccount: '',
      billingOption: 'sameAddress',
      billingStreetNumber: '',
      billingStreetAddress: '',
      billingAptOrUnit: '',
      billingCity: '',
      billingState: '',
      billingZipCode: '',
      poBox: '',
      poBoxCity: '',
      poBoxState: '',
      poBoxZipCode: '',
      isSecondaryAccountHolder: false,
      isPaperlessBilling: false,
      paperlessBillingEmail: '',
      secondaryAccountFirstName: '',
      secondaryAccountLastName: '',
      display_text: selectedAddress?.display_text,
      unit: selectedAddress?.unit,
      // isRenewableEnergy: false,
    },
    validate: zodResolver(finalSchema),
    validateInputOnChange: true,
    validateInputOnBlur: true,
  });

  // const SelectedESIIDStatus = async () => {
  //   const response = await axios.get('/api/myaccount/transfer/pendingtransactionstatus', {
  //     params: {
  //       esiid: form.values.esiid,
  //     },
  //   });
  //   return response?.data;
  // };

  async function submitForm() {
    form.validate();
    if (form.isValid()) {
      openModal();
      try {
        // As per the praney suggestion we hide the pendingtransactionstatus api call
        // const SelectedESIIDStatusResponse = await SelectedESIIDStatus();
        // const NewAddressPartnerNumber =
        //   SelectedESIIDStatusResponse?.result?.partnerNumber?.length === 8
        //     ? '00' + SelectedESIIDStatusResponse?.result?.partnerNumber
        //     : SelectedESIIDStatusResponse?.result?.partnerNumber;
        // if (SelectedESIIDStatusResponse?.result?.isActive && NewAddressPartnerNumber !== bpNumber) {
        //   router.push({ pathname: props.fields?.ActiveESIIDRedirectionLink?.value?.href });
        //   return;
        // } else {
        const req = await axios.post<
          AddHomeNextResponse,
          AxiosResponse<AddHomeNextResponse, AddHomeNextReqBody>,
          AddHomeNextReqBody
        >('/api/myaccount/add/home', {
          isExistingBill: form.values.isExistingBill === 'true',
          bpNumber,
          city: form.values.city,
          streetName: form.values.street,
          streetNumber: form.values.house_nbr,
          unit: form.values.unit,
          postalCode: form.values.zip,
          state: form.values.state,
          country: 'US',
          startDate: form.values.startdate,
          esiid: form.values.esiid,
          language: 'English',
          contractAccountNumber: form.values.contractAccount,
          customerIntent: cint === '6' ? 'MoveIn' : 'Switch',
          dwellingType: DwellingType[dwel],
          productId: selectedPlan?.planId,
          campaignId: selectedPlan?.campaignId,
          incentiveId: selectedPlan?.incentiveId,
          promoCode: prom ? prom : '',
          channel: 'Web',
          vendorId: '',
          billingOption: form.values.billingOption,
          billingStreetNumber: form.values.billingStreetNumber,
          billingStreetAddress: form.values.billingStreetAddress,
          billingAptOrUnit: form.values.billingAptOrUnit,
          billingCity: form.values.billingCity,
          billingState: form.values.billingState,
          billingZipCode: form.values.billingZipCode,
          poBox: form.values.poBox,
          poBoxCity: form.values.poBoxCity,
          poBoxState: form.values.poBoxState,
          poBoxZipCode: form.values.poBoxZipCode,
          isPaperlessBilling: form.values.isPaperlessBilling,
          paperlessEmail: form.values.paperlessBillingEmail,
          secondaryAccountFirstName: form.values.secondaryAccountFirstName,
          secondaryAccountLastName: form.values.secondaryAccountLastName,
          enrollDate: selectedPlan?.enrollDate,
        });
        if (req.data?.connectValidateResponse?.result?.indicator === 'MovingInOwnPremise') {
          router.push({
            pathname: props.fields.MovingInOwnPremiseLink.value.href,
            query: {
              ...router.query,
            },
          });
          return;
        }
        if (req.data?.connectValidateResponse?.messages[0] == 'SwitchHoldPending') {
          router.push({
            pathname: props.fields.SwitchHoldLink.value.href,
            query: {
              ...router.query,
            },
          });
          return;
        }

        let billingAddress = '';

        if (form.values.billingOption === 'sameAddress') billingAddress = form.values.display_text;
        else if (form.values.billingOption === 'differentAddress')
          billingAddress = `${form.values.billingAptOrUnit} ${form.values.billingStreetNumber} ${form.values.billingStreetAddress} ${form.values.billingCity} ${form.values.billingState} ${form.values.billingZipCode}`;
        else if (form.values.billingOption === 'poBox')
          billingAddress = `${form.values.poBox} ${form.values.poBoxCity} ${form.values.poBoxState} ${form.values.poBoxZipCode}`;
        if (!isPageEditing) {
          dispatch(
            setAddHomeInfo({
              billingAddress: billingAddress,
              paperlessBilling: form.values.isPaperlessBilling,
              secondaryAccountHolder: `${form.values.secondaryAccountFirstName} ${form.values.secondaryAccountLastName}`,
              startDate: form.values.startdate,
              serviceAddress: `${form.values.display_text}`,
              contractAccount: `${
                req.data.createContractResponse?.result.contractAccount
                  ? req.data.createContractResponse?.result.contractAccount
                  : form.values.contractAccount
              }`,
              serviceAccount: req.data.connectResponse.result.serviceContractNumber,
              // isRenewableEnergy: form.values.isRenewalEnergy,
            })
          );
        }
        router.push({
          pathname: props.fields.RedirectionLink.value.href,
          query: {
            ...router.query,
            bpNumber,
            contractaccount: form.values.contractAccount,
          },
        });
        // }
      } catch (error: unknown) {
        const err = error as AxiosError;
        logErrorToAppInsights(err, {
          componentStack: 'AddOrderInfoContainer-AddHome',
        });
        const queryParams = {
          errorCode: 'AddOrderInfoContainer-AddHome',
        };
        router.push({
          pathname: '/oops',
          query: { ...queryParams },
        });
      }
    } else {
      console.log(form.errors);
      const elem = document.querySelector<HTMLInputElement>(`[aria-invalid=true]`);
      if (elem) {
        elem.scrollIntoView({ behavior: `smooth`, block: 'center' });
        elem.focus();
      }
    }
  }

  return (
    <form className="basis-9/12 ml-0 sm:ml-[410px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6">
      <Placeholder
        name="jss-add-orderinfo"
        rendering={props.rendering}
        form={form}
        render={(components) => {
          return (
            <div className="flex flex-col items-center">
              {components.map((component, index) => {
                return (
                  <div
                    className={`${
                      index >= 2 ? 'px-5 sm:px-0 w-full sm:pr-[50px] mt-[25px]' : 'w-full'
                    }`}
                    key={index}
                  >
                    {component}
                  </div>
                );
              })}
            </div>
          );
        }}
      />

      <div className="px-5 sm:px-0 w-full mt-8 sm:pr-[50px] m-auto sm:m-0">
        <Button
          className="m-auto sm:m-0 my-8 mb-16 sm:mt-8 sm:mb-16"
          type="button"
          onClick={submitForm}
        >
          {props.fields.ButtonText.value}
        </Button>
      </div>
    </form>
  );
};

export { AddOrderInfoContainer };
// const Component = withDatasourceCheck()<AddOrderInfoContainerProps>(AddOrderInfoContainer);
export default aiLogger(AddOrderInfoContainer, AddOrderInfoContainer.name);
