import { Group, Select as MantineSelect, Radio, TextInput } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { Field, Text, withDatasourceCheck } from '@sitecore-jss/sitecore-jss-nextjs';
import DateSelector from 'components/OrderInformation/DateSelector/DateSelector';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { OrderInfoFormType } from '../OrderInformationContainer/OrderInformationContainer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/pro-light-svg-icons';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons';
import { Translation } from 'src/types/global';
import router from 'next/router';

export interface Languages {
  fields: {
    Title: Field<string>;
    Label: Field<string>;
  };
}

type CustomerInformationProps = ComponentProps & {
  fields: {
    CustomerInformationTitleText: Field<string>;
    MandatoryInfoText: Field<string>;
    FirstNameText: Translation;
    LastNameText: Translation;
    EmailAddressText: Translation;
    DateOfBirthText: Translation;
    PhoneNumberText: Translation;
    MobileOrNotText: Translation;
    PrefferedLanguageText: Translation;
    NextButtonText: Field<string>;
    NextButtonLink: Field<string>;
    DateOfBirthStartYear: Field<number>;
    DateOfBirthEndYear: Field<number>;
    DatePlaceHolder: Translation;
    MonthPlaceHolder: Translation;
    YearPlaceHolder: Translation;
    YesLabel: Translation;
    NoLabel: Translation;
    PrefferedLanguages: Languages[];
    ReferralTitleText: Field<string>;
    ReferralLabel: Field<string>;
    EnableReferralCode: Field<Boolean>;
  };
  form: UseFormReturnType<OrderInfoFormType>;
  isFocused: boolean;
};

const CustomerInformation = (props: CustomerInformationProps): JSX.Element => {
  const { refid } = router.query;
  function isValidEmail(email: string): boolean {
    const emailRegex = /^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/;
    return emailRegex.test(email);
  }

  const formatPhoneNumber = (event: React.ChangeEvent<HTMLInputElement>) => {
    let inputValue = event.target.value.replace(/\D/g, '');
    const formattedValue = [];
    if (inputValue.length > 3) {
      formattedValue.push(inputValue.substr(0, 3));
      inputValue = inputValue.substr(3);
    }
    if (inputValue.length > 3) {
      formattedValue.push(inputValue.substr(0, 3));
      inputValue = inputValue.substr(3);
    }
    formattedValue.push(inputValue);
    const formattedPhoneNumber = formattedValue.join('-');
    event.target.value = formattedPhoneNumber;
    props.form.setFieldValue('phoneNumber', formattedPhoneNumber);
  };

  return (
    <div className="order-information mt-7 flex flex-col gap-3 sm:gap-6 w-full sm:w-[800px] px-6 sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px] mb-4">
      <div className="flex gap-4">
        <Text
          tag="h2"
          className="font-primaryBlack text-textQuattuordenary sm:text-plus2 text-base -tracking-[0.25px]  sm:-tracking-[0.46px]"
          field={props.fields.CustomerInformationTitleText}
        />
        <Text
          tag="p"
          className="font-primaryBold text-minus3 text-textDuodenary mt-2"
          field={props.fields.MandatoryInfoText}
        />
      </div>
      <div className="flex flex-col gap-5 sm:gap-6">
        <div className="flex flex-col gap-5 sm:flex-row sm:gap-8 sm:items-start">
          <TextInput
            sx={{
              width: '100%',
              ['@media (min-width: 640px)']: {
                width: '384px',
              },
            }}
            styles={{
              label: {
                fontWeight: 500,
                fontFamily: 'OpenSans-Bold',
              },
            }}
            label={props.fields.FirstNameText.fields.Message.value}
            withAsterisk
            {...props.form.getInputProps('firstName')}
            onBlur={(event) => {
              props.form.validateField('firstName');
              window.adobeDataLayer.FirstName = event.target.value;
            }}
            className=""
          />
          <TextInput
            sx={{
              width: '100%',
              ['@media (min-width: 640px)']: {
                width: '384px',
              },
            }}
            styles={{
              label: {
                fontWeight: 500,
                fontFamily: 'OpenSans-Bold',
              },
            }}
            label={props.fields.LastNameText.fields.Message.value}
            withAsterisk
            {...props.form.getInputProps('lastName')}
            onBlur={(event) => {
              props.form.validateField('lastName');
              window.adobeDataLayer.LastName = event.target.value;
            }}
            className=""
          />
        </div>
        <div className="flex flex-col gap-5 sm:flex-row sm:gap-8 sm:items-start">
          <TextInput
            sx={{
              width: '100%',
              ['@media (min-width: 640px)']: {
                width: '384px',
              },
            }}
            styles={{
              label: {
                fontWeight: 500,
                fontFamily: 'OpenSans-Bold',
              },
            }}
            label={props.fields.EmailAddressText.fields.Message.value}
            withAsterisk
            className=""
            rightSection={
              <FontAwesomeIcon
                icon={faCircleCheck}
                className={`text-textSexdenary mr-[4px] text-plus2 ${
                  isValidEmail(props.form.values.email) ? 'visible' : 'invisible'
                }`}
              />
            }
            {...props.form.getInputProps('email')}
            onBlur={(event) => {
              props.form.validateField('email');
              window.adobeDataLayer.EmailAddress = event.target.value;
            }}
          />
          <DateSelector
            label={props.fields.DateOfBirthText.fields.Message.value}
            form={props.form}
            DateOfBirthEndYear={props.fields.DateOfBirthEndYear}
            DateOfBirthStartYear={props.fields.DateOfBirthStartYear}
            DatePlaceHolder={props.fields.DatePlaceHolder.fields.Message.value}
            MonthPlaceHolder={props.fields.MonthPlaceHolder.fields.Message.value}
            YearPlaceHolder={props.fields.YearPlaceHolder.fields.Message.value}
          />
        </div>
        <div className="flex flex-col gap-5 sm:flex-row sm:gap-8">
          {/* <NumberInput
            sx={{
              width: '216px',
            }}
            label={props.fields.PhoneNumberText.value}
            withAsterisk
            hideControls
            maxLength={10}
            parser={(value) => value.replaceAll('-', '')}
            defaultValue={''}
            rightSection={
              <FaCircleCheck
                className={`text-success mr-[4px] ${
                  props.form.values.phoneNumber !== undefined &&
                  isValidPhoneNumber(props.form.values.phoneNumber)
                    ? 'visible'
                    : 'invisible'
                }`}
                size={24}
              />
            }
            formatter={(value) =>
              !Number.isNaN(parseInt(value))
                ? `${value}`.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3')
                : ''
            }
            {...props.form.getInputProps('phoneNumber')}
          /> */}
          <TextInput
            sx={{
              width: '216px',
            }}
            label={props.fields.PhoneNumberText.fields.Message.value}
            withAsterisk
            maxLength={12}
            required
            defaultValue={''}
            value={props.form.values.phoneNumber}
            styles={{
              label: {
                fontWeight: 500,
                fontFamily: 'OpenSans-Bold',
              },
            }}
            rightSection={
              <FontAwesomeIcon
                icon={faCircleCheck}
                className={`text-textSexdenary mr-[4px] text-plus2 ${
                  props.form.values.phoneNumber.replace(/\D/g, '').length === 10
                    ? 'visible'
                    : 'invisible'
                }`}
              />
            }
            onChange={(event) => formatPhoneNumber(event)}
            onBlur={(event) => formatPhoneNumber(event)}
            error={props.form.errors.phoneNumber}
          />
          <div className="flex flex-col gap-[18px]">
            <Radio.Group
              {...props.form.getInputProps('isMobile')}
              label={`${props.fields.MobileOrNotText.fields.Message.value} *`}
              className=""
              styles={{
                label: {
                  fontWeight: 500,
                  fontFamily: 'OpenSans-Bold',
                },
              }}
            >
              <Group mt={15}>
                <Radio
                  value="yes"
                  label={props.fields.YesLabel.fields.Message.value}
                  styles={{
                    label: {
                      fontWeight: 500,
                      fontFamily: 'OpenSans-Regular',
                    },
                    root: {
                      marginTop: '-10px',
                    },
                  }}
                />
                <Radio
                  value="no"
                  label={props.fields.NoLabel.fields.Message.value}
                  styles={{
                    label: {
                      fontWeight: 500,
                      fontFamily: 'OpenSans-Regular',
                    },
                    root: {
                      marginTop: '-10px',
                    },
                  }}
                />
              </Group>
            </Radio.Group>
          </div>
          <MantineSelect
            sx={{ width: '280px' }}
            {...props.form.getInputProps('correspondanceLanguage')}
            label={props.fields.PrefferedLanguageText.fields.Message.value}
            data={props.fields.PrefferedLanguages?.map((language) => ({
              value: language?.fields?.Title?.value,
              label: language?.fields?.Label?.value,
            }))}
            styles={{
              label: {
                fontWeight: 500,
                fontFamily: 'OpenSans-Bold',
              },
              rightSection: { pointerEvents: 'none' },
            }}
            rightSection={
              <FontAwesomeIcon
                icon={faChevronDown}
                className=" text-textQuattuordenary hover:text-textQuattuordenary"
              />
            }
            className=""
            withAsterisk
          />
        </div>
        {props?.fields?.EnableReferralCode?.value === true && (
          <>
            <p>{props.fields.ReferralTitleText.value}</p>
            <TextInput
              value={refid}
              label={props.fields.ReferralLabel.value}
              defaultValue={refid}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                props.form.setFieldValue('AmbitReferralId', e.target.value);
              }}
              error={props.form.errors.AmbitReferralId}
              className="primaryBold text-lg text-textQuattuordenary md:w-[216px]"
            />
          </>
        )}
      </div>
    </div>
  );
};
export { CustomerInformation };
const Component = withDatasourceCheck()<CustomerInformationProps>(CustomerInformation);
export default aiLogger(Component, Component.name);
