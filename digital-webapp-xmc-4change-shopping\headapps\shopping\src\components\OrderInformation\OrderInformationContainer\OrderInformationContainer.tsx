import { Modal } from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import {
  Field,
  GetServerSideComponentProps,
  LinkField,
  Placeholder,
  useComponentProps,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios, { AxiosError } from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { encode } from 'querystring';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import usePreventAbandon from 'src/hooks/usePreventAbandon';
import { Plan } from 'src/services/FetchPlansAPI/types';
import { ValidateOTPRequest } from 'src/services/OOWAPI/types';
import { Offers } from 'src/services/ViewOffersAPI/types';
import {
  clearEnrollment,
  clearOOW,
  setBillingInfo,
  setCorrelationId,
  setCustomerInfo,
  setDepositAmount,
  setDRSToken,
  setEnrollmentInfo,
  setIdentityInfo,
  setIsDepositRequired,
  setOOWDLInfo,
  setOOWInfo,
  setPaperLessBilling,
  setPriorDebtInfo,
  setServiceInfo,
  setErrorMessage,
  setErrorCode,
  setDRSActionId,
  setSolarInfo,
  setRenewableEnergy,
  setAutopayEligible,
} from 'src/stores/enrollmentSlice';
import { clearAddonPlans, setSelectedPlan } from 'src/stores/planSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import getPlans from 'src/utils/getPlans';
import { localeMap } from 'src/utils/locale-mapping';
import { CustomerIntent, DwellingType, QueryParamsMapType } from 'src/utils/query-params-mapping';
import { v4 } from 'uuid';
import { z } from 'zod';
import { ActionResponse, Translation } from 'src/types/global';
import { FraudServiceBody } from 'src/services/EnrollmentAPI/types';
import { decryptURL, getANumber } from 'src/utils/util';

dayjs.extend(utc);

type OrderInformationContainerProps = ComponentProps & {
  fields: {
    PlaceOrderPageLink: LinkField;
    PlaceOrderDepositPageLink: LinkField;
    PlaceOrderPriorDebtPageLink: LinkField;
    MviPlaceOrderPageLink: LinkField;
    MviPlaceOrderDepositPageLink: LinkField;
    MviPlaceOrderPriorDebtPageLink: LinkField;
    SwitchHoldPageLink: LinkField;
    SwitchToMoveCloseLink: LinkField;
    ContinueButtonLabel: Field<string>;
    DateOfBirthStartYear: Field<number>;
    DateOfBirthEndYear: Field<number>;
    OOWValidateOTPPageLink: LinkField;
    OOWKIQPageLink: LinkField;
    //#region Error Messages
    FirstNameError: Translation;
    LastNameError: Translation;
    InvalidEmailError: Translation;
    PhoneNumberError: Translation;
    PhoneNumberInvalidError: Translation;
    CorrenspondanceLanguageError: Translation;
    DayError: Translation;
    MonthError: Translation;
    YearError: Translation;
    IsMobileError: Translation;
    SocialSecurityNumberError: Translation;
    DriverLicenseNumberError: Translation;
    DriverLicenseStateError: Translation;
    ServiceAddressError: Translation;
    BillingStreetNumberError: Translation;
    BillingStreetNameError: Translation;
    BillingCityError: Translation;
    BillingUnitError: Translation;
    BillingZipError: Translation;
    BillingStateError: Translation;
    EmailCopiesValidationError: Translation;
    POBoxError: Translation;
    TypeOfHomeError: Translation;
    OwnHomeError: Translation;
    //#endregion
    OOWTowerDataSettings: DropLinkField;
    ActiveESIIDRedirectionLink: LinkField;
    MovingInOwnPremiseLink: LinkField;
    EsiFutureMoveInContractsLink: LinkField;
    GeneralOopsRedirectLink: LinkField;
    FraudMatchRedirectLink: LinkField;
    EnableDRS: Field<string>;
    DRSActionName: Field<string>;
    EnableFraudService: Field<boolean>;
    EnableCreditAuthorization: Field<boolean>;
    CreditAuthorizationText: Field<string>;
    ReferralCodeError: Field<string>;
    SendOTPOopsPageLink: LinkField;
  };
};

export interface OrderInfoFormType {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  isMobile: string;
  correspondanceLanguage: string;
  dateOfBirth: string | null;
  date: string;
  month: string;
  year: string;
  identityOption: 'ssn' | 'dl';
  billingOption: 'sameAddress' | 'differentAddress' | 'poBox';
  address: string;
  serviceAddress: string;
  esiid: string;
  houseNbr: string;
  street: string;
  city: string;
  state: string;
  socialSecurityNumber: string;
  driverLicenseNumber: string;
  driverLicenseState: string;
  postalCode: string;
  billingStreetNumber: string;
  billingStreetAddress: string;
  billingAptOrUnit: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  poBox: string;
  poBoxCity: string;
  poBoxState: string;
  poBoxZipCode: string;
  secondaryAccountFirstName: string;
  secondaryAccountLastName: string;
  isEmailAddressCopies: boolean;
  emailCopiesList: { email: string }[];
  selectStartDate: string;
  unit: string;
  tdsp: string;
  paperlessBilling: boolean;
  solarconsultantemail: string;
  hometype: string;
  ownHome: string;
  isSolarAdded: boolean;
  creditCheckAuthorization: boolean;
  AmbitReferralId?: string;
  isRenewableEnergy: boolean;
}

export interface PlanResult {
  plan: Offers;
  planContent: Plan;
  correlationid: string;
}

export interface DropLinkField {
  fields: {
    EnableOOW: Field<string>;
    EnableTowerData: Field<string>;
    IsProduction: Field<string>;
  };
}
export const isAMB = process.env.NEXT_PUBLIC_SITE_NAME === 'amb';

const OrderInformationContainer = (props: OrderInformationContainerProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let correlationId = '';
  let solarAddedPlan = '';
  let startDate = '';
  let selectedPlan = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;
  if (!isPageEditing) {
    correlationId = useAppSelector((state) => state.enrollment?.correlationid);
    solarAddedPlan = useAppSelector((state) => state?.plans?.solarAdded);
    startDate = useAppSelector((state) => state?.enrollment?.startDate);
    selectedPlan = useAppSelector((state) => state?.plans?.selectedPlan);
    dispatch = useAppDispatch();
  }
  const [snowFlakeSessionId] = useState(() => v4());
  const router = useRouter();
  let token = '';
  const { dwel, cint, prom, rdcampaign, risktype, cnumber, anumber, web_experienceid, vendorid } =
    router.query as QueryParamsMapType;
  const customerInfoSchema = z.object({
    firstName: z.string().nonempty({ message: props.fields.FirstNameError.fields.Message.value }),
    lastName: z.string().nonempty({ message: props.fields.LastNameError.fields.Message.value }),
    email: z.string().email({ message: props.fields.InvalidEmailError.fields.Message.value }),
    phoneNumber: z
      .string()
      .regex(/^\d{3}-\d{3}-\d{4}$/, {
        message: props.fields.PhoneNumberInvalidError.fields.Message.value,
      })
      .refine((value) => value.replace(/\D/g, '').length === 10, {
        message: props.fields.PhoneNumberInvalidError.fields.Message.value,
        path: ['phoneNumber'],
      })
      .refine((value) => /^[0-9-]+$/.test(value), {
        message: props.fields.PhoneNumberInvalidError.fields.Message.value,
        path: ['phoneNumber'],
      }),

    correspondanceLanguage: z
      .string()
      .nonempty({ message: props.fields.CorrenspondanceLanguageError.fields.Message.value }),
    dateOfBirth: z.string().nonempty({ message: props.fields.DayError.fields.Message.value }),
    isMobile: z.string().nonempty({ message: props.fields.IsMobileError.fields.Message.value }),
    secondaryAccountFirstName: z.string().optional(),
    secondaryAccountLastName: z.string().optional(),
  });

  const referralSchema = z.object({
    AmbitReferralId: z
      .string()
      .optional()
      .refine((val) => val === undefined || val === '' || /^[a-zA-Z0-9]+$/.test(val), {
        message: props?.fields?.ReferralCodeError?.value || 'Referral code must be alphanumeric',
      }),
  });

  const identityInfoSchema = z.discriminatedUnion('identityOption', [
    z.object({
      identityOption: z.literal('ssn'),
      socialSecurityNumber: isAMB
        ? z.string().optional() // Not required when isAMB is true
        : z
            .string()
            .nonempty({ message: props.fields.SocialSecurityNumberError.fields.Message.value }) // Required when isAMB is false
            .refine((val) => /^\d{3}-\d{2}-\d{4}$/.test(val), {
              message: props.fields.SocialSecurityNumberError.fields.Message.value,
            }),
    }),

    z.object({
      identityOption: z.literal('dl'),
      driverLicenseNumber: isAMB
        ? z.string().optional() // Not required when isAMB is true
        : z
            .string()
            .regex(
              new RegExp('^[a-zA-Z0-9]+$'),
              props.fields.DriverLicenseNumberError.fields.Message.value
            ),
      driverLicenseState: isAMB
        ? z.string().optional() // Not required when isAMB is true
        : z
            .string()
            .nonempty({ message: props.fields.DriverLicenseStateError.fields.Message.value }),
    }),
  ]);

  const serviceInfoSchema = z.object({
    serviceAddress: z
      .string()
      .nonempty({ message: props.fields.ServiceAddressError.fields.Message.value }),
    selectStartDate: z.coerce.date({ errorMap: () => ({ message: 'Required' }) }),
  });

  const billingInfoSchema = z.discriminatedUnion('billingOption', [
    z.object({
      billingOption: z.literal('sameAddress'),
    }),
    z.object({
      billingOption: z.literal('differentAddress'),
      billingStreetNumber: z
        .string()
        .nonempty({ message: props.fields.BillingStreetNumberError.fields.Message.value }),
      billingStreetAddress: z
        .string()
        .nonempty({ message: props.fields.BillingStreetNameError.fields.Message.value }),
      billingAptOrUnit: z.string(),
      // dwel === '02'
      //   ? z.string().nonempty({ message: props.fields.BillingUnitError.fields.Message.value })
      //   : z.string(),
      billingCity: z
        .string()
        .nonempty({ message: props.fields.BillingCityError.fields.Message.value }),
      billingState: z
        .string()
        .nonempty({ message: props.fields.BillingStateError.fields.Message.value }),
      billingZipCode: z
        .string()
        .nonempty({ message: props.fields.BillingZipError.fields.Message.value }),
    }),
    z.object({
      billingOption: z.literal('poBox'),
      poBox: z.string().nonempty({ message: props.fields.POBoxError.fields.Message.value }),
      poBoxCity: z
        .string()
        .nonempty({ message: props.fields.BillingCityError.fields.Message.value }),
      poBoxState: z
        .string()
        .nonempty({ message: props.fields.BillingStateError.fields.Message.value }),
      poBoxZipCode: z
        .string()
        .nonempty({ message: props.fields.BillingZipError.fields.Message.value }),
    }),
  ]);

  const emailCopiesSchema = z.object({
    emailCopiesList: z.array(
      z.object({
        email: z
          .string()
          .email({ message: props.fields.EmailCopiesValidationError.fields.Message.value })
          .or(z.literal('')),
      })
    ),
  });

  const solarInformationSchema = z
    .object({
      hometype: z.string().optional(),
      ownHome: z.string().optional(),
      isSolarAdded: z.boolean(),
    })
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
    .superRefine((data: any, ctx: z.RefinementCtx) => {
      if (data.isSolarAdded === true) {
        if (data.hometype === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.IsMobileError.fields.Message.value,
            path: ['hometype'],
          });
        }
        if (data.hometype === 'Apartment' || data.hometype === 'Mobile') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.TypeOfHomeError.fields.Message.value,
            path: ['hometype'],
          });
        }
        if (data.ownHome === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.IsMobileError.fields.Message.value,
            path: ['ownHome'],
          });
        }
        if (data.ownHome === 'no') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.OwnHomeError.fields.Message.value,
            path: ['ownHome'],
          });
        }
      }
    });

  const finalSchema = solarAddedPlan
    ? customerInfoSchema
        .and(identityInfoSchema)
        .and(serviceInfoSchema)
        .and(billingInfoSchema)
        .and(emailCopiesSchema)
        .and(solarInformationSchema)
        .and(referralSchema)
    : customerInfoSchema
        .and(identityInfoSchema)
        .and(serviceInfoSchema)
        .and(billingInfoSchema)
        .and(emailCopiesSchema)
        .and(referralSchema);

  const [showModal, setShowMismatchModal] = useState(false);

  const [isFocused, setFocusOnContinue] = useState(false);
  const [isSwitchToMoveIn, setisSwitchToMoveIn] = useState(false);
  const [isCustomerValid, setIsCustomerValid] = useState(false);
  const IsOOWEnabled = props.fields.OOWTowerDataSettings.fields.EnableOOW.value;
  const IsTowerDataON = props.fields.OOWTowerDataSettings.fields.EnableTowerData.value;
  const IsProduction = props.fields.OOWTowerDataSettings.fields.IsProduction.value;
  const isRdCampaign = rdcampaign === 'true';
  const plandata = useComponentProps<PlanResult>(props.rendering.uid);
  const allowedUrls: (string | undefined)[] = [
    props.fields.PlaceOrderPageLink.value.href,
    props.fields.PlaceOrderDepositPageLink.value.href,
    props.fields.PlaceOrderPriorDebtPageLink.value.href,
    props.fields.MviPlaceOrderPageLink.value.href,
    props.fields.MviPlaceOrderDepositPageLink.value.href,
    props.fields.MviPlaceOrderPriorDebtPageLink.value.href,
    props.fields.SwitchHoldPageLink.value.href,
    props.fields.SwitchToMoveCloseLink.value.href,
    props.fields.OOWValidateOTPPageLink.value.href,
    props.fields.OOWKIQPageLink.value.href,
    props.fields.ActiveESIIDRedirectionLink.value.href,
    props.fields.MovingInOwnPremiseLink.value.href,
    props.fields.FraudMatchRedirectLink.value.href,
    props.fields.GeneralOopsRedirectLink.value.href,
    '/oops',
    '/connect-validate-oops',
  ];
  usePreventAbandon(allowedUrls);
  const [submitDisabled, { open: disableSubmit, close: enableSubmit }] = useDisclosure(false);

  useEffect(() => {
    form.setFieldValue('isSolarAdded', solarAddedPlan);
  }, [solarAddedPlan]);

  useEffect(() => {
    if (isRdCampaign && plandata?.plan) {
      if (plandata.correlationid)
        if (!isPageEditing) {
          dispatch(setCorrelationId(plandata?.correlationid));
          dispatch(
            setSelectedPlan({
              ...selectedPlan,
              planId: plandata?.plan.id,
              planName: plandata?.plan.name,
              rate: plandata?.plan.baseRate,
              term: plandata?.plan.term,
              cancellationFee: plandata?.plan.cancellationFee,
              EFLUrl: plandata?.plan.electricityFactsLink,
              TOSUrl: plandata?.plan.termsOfServiceLink,
              YRCUrl: plandata?.plan.yourRightsLink,
              campaignId: plandata?.plan.campaignId,
              incentiveId: plandata?.plan.incentiveId,
              incentiveDisclaimer: plandata?.plan.incentiveDisclaimer,
              incentiveText: plandata?.plan.incentiveSpecialOfferText,
              totalGreenUp: plandata?.plan.totalGreenUp,
              ev: plandata?.plan.ev,
              enrollDate: plandata?.plan.enrollDate,
              rateType: plandata?.plan.rateType,
              planBenefits: plandata?.plan.planBenefits,
              planDisclaimer: plandata?.plan.planDisclaimer,
            })
          );
        }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!isPageEditing) {
      dispatch(clearEnrollment());
      dispatch(clearAddonPlans());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const initialValues: OrderInfoFormType = {
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    isMobile: '',
    correspondanceLanguage: '',
    dateOfBirth: '',
    date: '',
    month: '',
    year: '',
    identityOption: 'ssn',
    billingOption: 'sameAddress',
    address: '',
    serviceAddress: '',
    esiid: '',
    houseNbr: '',
    street: '',
    city: '',
    state: '',
    socialSecurityNumber: '',
    driverLicenseNumber: '',
    driverLicenseState: '',
    postalCode: '',
    billingStreetNumber: '',
    billingStreetAddress: '',
    billingAptOrUnit: '',
    billingCity: '',
    billingState: '',
    billingZipCode: '',
    poBox: '',
    poBoxCity: '',
    poBoxState: '',
    poBoxZipCode: '',
    secondaryAccountFirstName: '',
    secondaryAccountLastName: '',
    isEmailAddressCopies: false,
    emailCopiesList: [{ email: '' }],
    selectStartDate: '',
    unit: '',
    tdsp: '',
    paperlessBilling: false,
    solarconsultantemail: '',
    hometype: '',
    ownHome: '',
    isSolarAdded: solarAddedPlan,
    creditCheckAuthorization: false,
    AmbitReferralId: '',
    isRenewableEnergy: false,
  };

  const [formConfig, setFormConfig] = useState<{
    initialValues: OrderInfoFormType;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    validationSchema: z.ZodIntersection<any, any>;
  }>({
    initialValues: initialValues,
    validationSchema: finalSchema,
  });

  const form = useForm<OrderInfoFormType>({
    initialValues: formConfig.initialValues,
    validate: zodResolver(formConfig.validationSchema),
    validateInputOnChange: [
      'firstName',
      'lastName',
      'phoneNumber',
      'isMobile',
      'correspondanceLanguage',
      'dateOfBirth',
      'identityOption',
      'billingOption',
      'address',
      'serviceAddress',
      'esiid',
      'houseNbr',
      'street',
      'city',
      'state',
      'socialSecurityNumber',
      'driverLicenseNumber',
      'driverLicenseState',
      'postalCode',
      'billingStreetNumber',
      'billingStreetAddress',
      'billingAptOrUnit',
      'billingCity',
      'billingState',
      'billingZipCode',
      'poBox',
      'poBoxCity',
      'poBoxState',
      'poBoxZipCode',
      'secondaryAccountFirstName',
      'secondaryAccountLastName',
      'isEmailAddressCopies',
      'emailCopiesList',
      'selectStartDate',
      'unit',
      'AmbitReferralId',
      'isRenewableEnergy',
    ],
    validateInputOnBlur: true,
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updateSchema = (newFields: Record<string, any>, newSchema: z.ZodObject<any, 'strip'>) => {
    setFormConfig((prevConfig) => ({
      initialValues: { ...prevConfig.initialValues, ...newFields },
      validationSchema: prevConfig.validationSchema.and(newSchema),
    }));
  };

  useEffect(() => {
    console.log(form.values);
    if (router.query.tdsp?.toString().toLowerCase() === form.values?.tdsp?.toLowerCase()) {
      setIsCustomerValid(true);
    } else {
      setIsCustomerValid(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.values.tdsp]);

  // scroll into the invalid logical form errors
  const onErrorFocus = () => {
    const elem = document.querySelector<HTMLInputElement>(`[aria-invalid=true]`);
    if (elem) {
      elem.scrollIntoView({ behavior: `smooth`, block: 'center' });
      elem.focus();
    }
  };

  const onSubmitDRSScriptEvent = (): Promise<string> => {
    return new Promise((resolve, reject) => {
      window.tsPlatform.drs
        .triggerActionEvent(props.fields.DRSActionName.value, {
          correlationId: `${correlationId}`,
        })
        .then((actionResponse: ActionResponse) => {
          console.log(`correlationId:${correlationId}`);
          if (!isPageEditing) {
            dispatch(setDRSToken(actionResponse.actionToken));
          }
          // Return the actionToken as a string
          resolve(actionResponse.actionToken);
        })
        .catch((error: AxiosError) => {
          console.error('Error:', error);
          reject(error); // Reject the promise if there's an error
        });
    });
  };

  //update customer info to store
  const customerInfoUpdate = () => {
    const customerInfo = {
      isMobile: form.values.isMobile === 'yes' ? true : false,
      correspondanceLanguage: form.values.correspondanceLanguage,
      dateOfBirth: dayjs.utc(form.values.dateOfBirth).format().toString(),
      email: form.values.email,
      firstName: form.values.firstName,
      lastName: form.values.lastName,
      phoneNumber: parseInt(form.values.phoneNumber.replaceAll('-', '')),
      emailCopiesList: form.values.emailCopiesList,
      AmbitReferralId: form.values.AmbitReferralId != undefined ? form.values.AmbitReferralId : '',
      mobileNumber: form.values.isMobile,
    };

    const identityInfo = {
      driverLicenseState: form.values.driverLicenseState,
      driverLicenseNumber: form.values.driverLicenseNumber,
      socialSecurityNumber: form.values.socialSecurityNumber.replaceAll('-', ''),
    };

    const serviceInfo = {
      esiid: form.values.esiid,
      poBox: form.values.poBox,
      houseNbr: form.values.houseNbr,
      street: form.values.street,
      city: form.values.city,
      state: form.values.state,
      postalCode: form.values.postalCode,
      poBoxCity: form.values.poBoxCity,
      poBoxState: form.values.poBoxState,
      poBoxZipCode: form.values.poBoxZipCode,
      startDate: startDate,
      unit: form.values.unit,
      tdsp: form.values.tdsp,
    };

    const billingInfo = {
      isSameAddress: form.values.billingOption === 'sameAddress',
      billingStreetNumber: form.values.billingStreetNumber,
      billingStreetAddress: form.values.billingStreetAddress,
      billingAptOrUnit: form.values.billingAptOrUnit,
      billingCity: form.values.billingCity,
      billingState: form.values.billingState,
      billingZipCode: form.values.billingZipCode,
      secondaryAccountFirstName: form.values.secondaryAccountFirstName,
      secondaryAccountLastName: form.values.secondaryAccountLastName,
    };

    const solarInfo = {
      solarConsultantEmail: form.values.solarconsultantemail,
      ownHome: form.values.ownHome === 'yes',
      homeType: form.values.hometype,
    };
    if (!isPageEditing) {
      dispatch(setCustomerInfo(customerInfo));
      dispatch(setIdentityInfo(identityInfo));
      dispatch(setServiceInfo(serviceInfo));
      dispatch(setBillingInfo(billingInfo));
      dispatch(setPaperLessBilling(form.values.paperlessBilling));
      dispatch(setRenewableEnergy(form.values.isRenewableEnergy));
      dispatch(setSolarInfo(solarInfo));
    }
  };

  const continueSubmit = async () => {
    await createCustomer('4', true, token);
    // setisSwitchToMoveIn(false);
  };

  const sentOTP = async (isMobile: boolean) => {
    const isoDateString = new Date().toISOString();
    const messageTime = isoDateString.substring(0, isoDateString.length - 5) + 'Z';
    let sendOTPReq = {};
    if (IsProduction) {
      sendOTPReq = {
        clientReferenceId: v4(),
        messageTime: messageTime,
        firstName: form.values.firstName,
        middleNames: '',
        surName: form.values.lastName,
        addressType: 'CURRENT',
        poBoxNumber: '',
        street: form.values.houseNbr + ' ' + form.values.street,
        street2: '',
        postTown: form.values.city,
        postal: form.values.postalCode,
        stateProvinceCode: form.values.state,
        DOB: '',
        PhoneNumber: '+1' + parseInt(form.values.phoneNumber.replaceAll('-', '')),
        email: form.values.email,
        SSN: form.values.socialSecurityNumber.replaceAll('-', ''),
        DL: form.values.driverLicenseNumber.trim(),
        ESIID: form.values.esiid,
        Language: localeMap[router.locale as string],
      };
    } else {
      if (risktype === 'high') {
        sendOTPReq = {
          clientReferenceId: v4(),
          messageTime: messageTime,
          firstName: 'THOMAS',
          middleNames: 'J',
          surName: 'CERNICH',
          addressType: 'CURRENT',
          poBoxNumber: '',
          street: '219 E 15th ST',
          street2: 'APT 2',
          postTown: 'MISSION',
          postal: '78572',
          stateProvinceCode: 'TX',
          DOB: '',
          PhoneNumber: '+12145290528',
          email: form.values.email,
          SSN: '666592549',
          DL: '',
          ESIID: form.values.esiid,
          Brand: 'Ambit',
          Language: localeMap[router.locale as string],
        };
      } else if (risktype === 'wrongphone') {
        sendOTPReq = {
          clientReferenceId: v4(),
          messageTime: messageTime,
          firstName: 'GORDON',
          middleNames: 'L',
          surName: 'BELL',
          addressType: 'CURRENT',
          poBoxNumber: '',
          street: '4826 DEBENEY DR',
          street2: '',
          postTown: 'HOUSTON',
          postal: '77039',
          stateProvinceCode: 'TX',
          DOB: '',
          PhoneNumber: '+15027679075',
          email: form.values.email,
          SSN: '566188322',
          DL: '',
          ESIID: form.values.esiid,
          Language: localeMap[router.locale as string],
        };
      } else {
        sendOTPReq = {
          clientReferenceId: v4(),
          messageTime: messageTime,
          firstName: 'GARY',
          middleNames: 'A',
          surName: 'LINDSAY',
          addressType: 'CURRENT',
          poBoxNumber: '',
          street: 'CMR 408 BX 1502',
          street2: '',
          postTown: 'APO',
          postal: '091829998',
          stateProvinceCode: 'AE',
          DOB: '',
          PhoneNumber: '+12145290528',
          email: form.values.email,
          SSN: '',
          DL: '',
          ESIID: form.values.esiid,
          Language: localeMap[router.locale as string],
        };
      }
    }

    const sendOTP = await axios.post('/api/oow/sendotp', sendOTPReq);
    if (isMobile) {
      if (sendOTP.data?.result && sendOTP.data?.result.indicator == 'Success') {
        //Low Risk Customer
        const oowDLInfo = {
          OOW_OTP_Sent: true,
          OOW_OTP_Response: 'Success',
          OOW_CustomerType: 'LowRisk',
          OOW_KIQ_Sent: false,
          OOW_KIQ_Reponse: '',
        };

        if (sendOTP.data?.result.isGoodtoProceedEnrollment) {
          await createCustomer('', false, token);
        } else {
          oowDLInfo.OOW_CustomerType = 'HighRisk';
          if (!isPageEditing) {
            dispatch(
              setOOWInfo({
                sessionID: sendOTP.data?.result.sendOtpResults.sessionID,
                clientReferenceId: sendOTP.data?.result.sendOtpResults.clientReferenceId,
                score: sendOTP.data?.result.sendOtpResults.score,
                decision: sendOTP.data?.result.sendOtpResults.decision,
                kba: [],
              })
            );
          }

          if (!IsProduction) {
            console.log('OTP:' + sendOTP.data?.result.sendOtpResults.oneTimePwd);
          }

          if (
            sendOTP.data?.result.isGoodtoProceedEnrollment == false &&
            sendOTP.data?.result.sendOtpResults.oneTimePwd != null &&
            sendOTP.data?.result.intialKba == null
          ) {
            router.push({
              pathname: props.fields.OOWValidateOTPPageLink.value.href,
              query: {
                ...router.query,
              },
            });
          } else if (
            sendOTP.data?.result.isGoodtoProceedEnrollment == false &&
            sendOTP.data?.result.sendOtpResults.oneTimePwd == null &&
            sendOTP.data?.result.intialKba != null
          ) {
            GetInitialKIQQuestions(sendOTP.data?.result);
          }
        }
        if (!isPageEditing) {
          dispatch(setOOWDLInfo(oowDLInfo));
        }
      } else if (sendOTP.data?.result && sendOTP.data?.result.indicator == 'Failure') {
        router.push({
          pathname: props?.fields?.SendOTPOopsPageLink?.value?.href,
          query: {
            ...router.query,
          },
        });
      } else {
        router.push({
          pathname: '/oops',
          query: {
            ...router.query,
          },
        });
      }
    } else {
      //sendOTP with fakeotp
      GetKIQQuestions(
        '111111',
        sendOTP.data?.result.sendOtpResults.sessionID,
        sendOTP.data?.result.sendOtpResults.score,
        sendOTP.data?.result.sendOtpResults.decision
      );
    }
  };

  const addOopsPageMessages = (oopsMessageObjectString: string) => {
    if (oopsMessageObjectString) {
      const oopsMessages = JSON.parse(oopsMessageObjectString);
      const webExperienceId =
        web_experienceid !== undefined ? web_experienceid : process.env.NEXT_PUBLIC_SITE_NAME;
      const errorMessage = oopsMessages.errorMessage.replace(
        'esiid-webExperienceId',
        `${form.values.esiid ?? 'esiid'}-${webExperienceId}`
      );
      if (!isPageEditing) {
        dispatch(setErrorMessage(errorMessage));
        dispatch(setErrorCode(oopsMessages.errorCode));
        dispatch(setDRSActionId(oopsMessages.DrsActionId));
      }
    }
  };

  const FraudCheck = async (): Promise<string> => {
    try {
      const messageTime = dayjs().toString();
      const fraudCheckRequestBody: FraudServiceBody = {
        firstName: form.values.firstName,
        lastName: form.values.lastName,
        intent: CustomerIntent[cint as string],
        esiid: form.values.esiid,
        email: form.values.email,
        phoneNumber: parseInt(form.values.phoneNumber.replaceAll('-', '')),
        clientReferenceId: v4(),
        messageTime: messageTime,
        middleName: '',
        addressType: 'CURRENT',
        poBoxNumber: '',
        street: form.values.street,
        street2: '',
        postTown: form.values.city,
        postal: form.values.postalCode,
        stateProvinceCode: form.values.state,
        DOB: form.values.dateOfBirth,
        SSN: isAMB ? form.values.socialSecurityNumber || '' : form.values.socialSecurityNumber, // Allow null if isAMB is true
        driverLicense: form.values.driverLicenseNumber,
        DRSActionToken: token,
        sessionId: snowFlakeSessionId,
        promoCode: typeof prom === 'string' ? prom : '',
      };
      const response = await axios.post('/api/fraud', fraudCheckRequestBody);
      addOopsPageMessages(response.data?.messages.slice(-1));
      if (response.data?.result && response.data?.result?.indicator == 'failure') {
        return 'fraudcheckfails';
      } else if (response.data?.result?.fraudMatch == 'Proceed') {
        return 'Success';
      } else if (response.data?.result?.fraudMatch == 'OOW') {
        return 'OOW';
      } else {
        return 'fraudmatch';
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log(err.toJSON());
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
        },
      });
      return '';
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const GetInitialKIQQuestions = (KIQResponse: any) => {
    const oowDLInfo = {
      OOW_OTP_Sent: true,
      OOW_OTP_Response: 'Failure',
      OOW_CustomerType: '',
      OOW_KIQ_Sent: true,
      OOW_KIQ_Reponse: '',
    };

    if (KIQResponse != null) {
      if (KIQResponse.indicator === 'Success' && KIQResponse.intialKba != null) {
        if (!isPageEditing) {
          dispatch(
            setOOWInfo({
              sessionID: KIQResponse.sendOtpResults.sessionID,
              clientReferenceId: KIQResponse.sendOtpResults.clientReferenceId,
              score: KIQResponse.sendOtpResults.score,
              decision: KIQResponse.sendOtpResults.decision,
              kba: KIQResponse.intialKba.questionSet,
            })
          );
        }
        router.push({
          pathname: props.fields.OOWKIQPageLink.value.href,
          query: {
            ...router.query,
          },
        });
      } else {
        if (!isPageEditing) {
          dispatch(clearOOW());
        }
        router.push({
          pathname: '/oops',
          query: {
            ...router.query,
          },
        });
      }
      if (!isPageEditing) {
        dispatch(setOOWDLInfo(oowDLInfo));
      }
    }
  };

  const GetKIQQuestions = async (
    otp: string,
    sessionID: string,
    score: string,
    decision: string
  ) => {
    const isoDateString = new Date().toISOString();
    const messageTime = isoDateString.substring(0, isoDateString.length - 5) + 'Z';

    const requestbody: ValidateOTPRequest = {
      clientReferenceId: v4(),
      messageTime: messageTime,
      sessionID: sessionID,
      oneTimePwd: otp,
      score: score,
      decision: decision,
      ssn: form.values.socialSecurityNumber.replaceAll('-', ''),
      Language: localeMap[router.locale as string],
      stateProvinceCode: form.values.state,
    };
    const oowDLInfo = {
      OOW_OTP_Sent: true,
      OOW_OTP_Response: 'Failure',
      OOW_CustomerType: '',
      OOW_KIQ_Sent: true,
      OOW_KIQ_Reponse: '',
    };

    const response = await axios.post('/api/oow/validateotp', requestbody);
    if (response.data?.result && response.data?.result.indicator === 'Failure') {
      if (!isPageEditing) {
        dispatch(
          setOOWInfo({
            sessionID: sessionID,
            clientReferenceId: requestbody.clientReferenceId,
            score: score,
            decision: decision,
            kba: response.data?.result?.kba?.questionSet,
          })
        );
      }
      router.push({
        pathname: props.fields.OOWKIQPageLink.value.href,
        query: {
          ...router.query,
        },
      });
    } else {
      if (!isPageEditing) {
        dispatch(clearOOW());
      }
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
        },
      });
    }
    if (!isPageEditing) {
      dispatch(setOOWDLInfo(oowDLInfo));
    }
  };

  const CheckTDandFraudCheck = async (): Promise<string> => {
    try {
      //CheckTD
      const tdReqBody = {
        FirstName: form.values.firstName,
        LastName: form.values.lastName,
        EsiId: form.values.esiid,
        Email: form.values.email,
        PhoneNumber: parseInt(form.values.phoneNumber.replaceAll('-', '')),
        skiptdvalidation: IsTowerDataON ? false : true,
        ConsultantId: getCNumber(),
      };

      const response = await axios.post('/api/oow/checkfraudtdvalidation', tdReqBody);
      if (response.data?.result && response.data?.result?.indicator == 'Failure') {
        return 'tdfails';
      } else if (!response.data?.result?.fraudMatch) {
        return 'Success';
      } else if (response.data?.result?.fraudMatch) {
        return 'fraudmatchtrue';
      } else {
        return 'fraudmatch';
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log(err.toJSON());
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
        },
      });
      return '';
    }
  };

  const ValidateOOW = async () => {
    const isMobile = form.values.isMobile === 'yes' ? true : false;
    sentOTP(isMobile);
  };

  function getCNumber() {
    const value = decryptURL(cnumber);
    const cno = value?.toString();
    if (cno !== undefined && cno !== '') {
      return cno.startsWith('c', 0) ? cno.replace(cno[0], 'C') : 'C' + cno;
    } else {
      return '';
    }
  }

  //create customer API call
  const createCustomer = async (
    intent: string,
    isMovinUrlUpdate: boolean,
    drsToken: string
  ): Promise<void> => {
    try {
      const { data: enrollmentData } = await axios.post('/api/customer', {
        email: form.values.email,
        phoneNumber: parseInt(form.values.phoneNumber.replaceAll('-', '')),
        isMobile: form.values.isMobile === 'yes' ? true : false,
        firstName: form.values.firstName,
        lastName: form.values.lastName,
        dateOfBirth: dayjs(form.values.dateOfBirth, 'MM/DD/YYYY').format(
          'YYYY-MM-DDTHH:mm:ss.SSS[Z]'
        ),
        streetNumber: form.values.houseNbr,
        correspondanceLanguage:
          form.values.correspondanceLanguage === 'Español'
            ? 'Spanish'
            : form.values.correspondanceLanguage,
        streetName: form.values.street,
        city: form.values.city,
        state: form.values.state,
        postalCode: form.values.postalCode,
        esiid: form.values.esiid,
        startDate: startDate,
        customerIntent: intent === '' ? CustomerIntent[cint as string] : CustomerIntent[intent],
        productId: selectedPlan.planId,
        promoCode: typeof prom === 'string' ? prom : '',
        campaignId: selectedPlan.campaignId,
        socialSecurityNumber: form.values.socialSecurityNumber.replaceAll('-', ''),
        driverLicenseNumber: form.values.driverLicenseNumber,
        driverLicenseState: form.values.driverLicenseNumber ? form.values.driverLicenseState : '',
        poBox: form.values.poBox,
        middleName: null,
        unitNumber: form.values.unit,
        dwellingType: DwellingType[dwel as string],
        incentiveId: selectedPlan.incentiveId,
        isMultiFamilyDwelling: DwellingType[dwel as string],
        channel: 'web',
        vendorId: vendorid === null || vendorid === undefined || vendorid === '' ? 'WEB' : vendorid,
        billingStreetNumber: form.values.billingStreetNumber,
        billingStreetAddress: form.values.billingStreetAddress,
        billingAptOrUnit: form.values.billingAptOrUnit,
        billingCity: form.values.billingCity,
        billingState: form.values.billingState,
        billingZipCode: form.values.billingZipCode,
        poBoxCity: form.values.poBoxCity,
        poBoxState: form.values.poBoxState,
        poBoxZipCode: form.values.poBoxZipCode,
        isSameAddress: form.values.billingOption === 'sameAddress',
        skiptdvalidation: true,
        enrollDate: selectedPlan.enrollDate,
        billingOption: form.values.billingOption,
        DRSActionToken: drsToken,
        sessionId: snowFlakeSessionId,
        CNumber: getCNumber(),
        ANumber: getANumber(anumber),
        WebExperienceId:
          web_experienceid !== undefined ? web_experienceid : process.env.NEXT_PUBLIC_SITE_NAME,
        Tdsp: selectedPlan?.tdsp?.split('_')?.[1],
        MFAReferralId: form.values.AmbitReferralId,
        Brand: process.env.NEXT_PUBLIC_BRAND ? process.env.NEXT_PUBLIC_BRAND.toUpperCase() : '',
        // isRenewableEnergy:form.values.isRenewableEnergy,
      });

      // const NewAddressPartnerNumber =
      //   enrollmentData?.SelectedESIIDStatusResponse?.result?.partnerNumber?.length === 8
      //     ? '00' + enrollmentData?.SelectedESIIDStatusResponse?.result?.partnerNumber
      //     : enrollmentData?.SelectedESIIDStatusResponse?.result?.partnerNumber;

      // if (
      //   enrollmentData?.SelectedESIIDStatusResponse?.result?.isActive &&
      //   NewAddressPartnerNumber !== enrollmentData.createCustomerResponse.result.partnerNumber
      // ) {
      //   router.push({
      //     pathname: props.fields.ActiveESIIDRedirectionLink.value.href,
      //   });
      //   return;
      // }

      //As per pranay and sandhana for vbb brands we are not stopping pending esiid

      // if (enrollmentData.SelectedESIIDStatusResponse?.result?.isPending) {
      //   router.push({
      //     pathname: props.fields.EsiFutureMoveInContractsLink?.value?.href,
      //   });
      //   return;
      // }

      // if (dwel === '02') {
      //   try {
      //     if (form.values.emailCopiesList.length > 0) {
      //       const emailCopiesPromises = form.values.emailCopiesList.map((receipientEmail) => {
      //         return axios.post('/api/email/confirmation', {
      //           Data: {
      //             FirstName: form.values.firstName,
      //             LastName: form.values.lastName,
      //             RecipientEmail: receipientEmail.email,
      //             EmailAddress: form.values.email,
      //             ServiceAddress: form.values.serviceAddress,
      //             PlanName: selectedPlan.planName,
      //             StartDate: startDate,
      //             ContractAccount:
      //               enrollmentData.createContractAccountResponse.result.contractAccount,
      //             BP: enrollmentData.createCustomerResponse.result.partnerNumber,
      //           },
      //         });
      //       });
      //       await Promise.allSettled(emailCopiesPromises);
      //     }
      //   } catch (err) {
      //     const error = err as AxiosError;
      //     console.log('ELeaseEmailConfirmation failed', error.toJSON());
      //   }
      // }

      if (enrollmentData.calculateDepositResponse != null) {
        if (!isPageEditing) {
          dispatch(setDepositAmount(enrollmentData.calculateDepositResponse.result));
        }
      }
      if (!isPageEditing) {
        dispatch(
          setIsDepositRequired(
            enrollmentData.checkSecurityDepositResponse.result?.isDepositRequired &&
              enrollmentData?.calculateDepositResponse?.result > 0
          )
        );

        dispatch(
          setAutopayEligible(enrollmentData.checkSecurityDepositResponse?.result?.autopayEligible)
        );
        dispatch(
          setEnrollmentInfo({
            bpNumber: enrollmentData.createCustomerResponse.result.partnerNumber,
            contractAccountNumber:
              enrollmentData.createContractAccountResponse.result.contractAccount,
            serviceAccountNumber: '',
          })
        );

        dispatch(
          setBillingInfo({
            isSameAddress: form.values.billingOption === 'sameAddress',
            billingStreetNumber: form.values.billingStreetNumber,
            billingStreetAddress: form.values.billingStreetAddress,
            billingAptOrUnit: form.values.billingAptOrUnit,
            billingCity: form.values.billingCity,
            billingState: form.values.billingState,
            billingZipCode: form.values.billingZipCode,
            secondaryAccountFirstName: form.values.secondaryAccountFirstName,
            secondaryAccountLastName: form.values.secondaryAccountLastName,
          })
        );
      }
      const commonParams = {
        streetNum: form.values.houseNbr,
        streetName: form.values.street,
        city: form.values.city,
        state: form.values.state,
        zip: form.values.postalCode,
        esiid: form.values.esiid,
        bpNumber: enrollmentData.createCustomerResponse.result.partnerNumber,
        cint: isMovinUrlUpdate ? '4' : router.query.cint,
        tdsp: form.values.tdsp,
        planid: selectedPlan.planId,
      };
      if (
        enrollmentData.connectValidateResponse.result.indicator === 'EnrollmentAlreadySubmitted'
      ) {
        router.push({
          pathname: '/connect-validate-oops',
          query: {
            ...router.query,
            ...commonParams,
            errorCode: enrollmentData.connectValidateResponse.result.indicator,
          },
        });
      } else if (
        enrollmentData.connectValidateResponse.result.indicator === 'ActiveContractExists'
      ) {
        enableSubmit();
        setIsCustomerValid(false);
        setisSwitchToMoveIn(true);
      } else if (enrollmentData.connectValidateResponse.result.indicator === 'MovingInOwnPremise') {
        {
          router.push({
            pathname: props.fields.MovingInOwnPremiseLink?.value?.href,
          });
        }
        return;
      } else if (enrollmentData.connectValidateResponse.result.indicator == 'Success') {
        if (enrollmentData.createCustomerResponse.result.fraudMatch) {
        } else if (enrollmentData.createCustomerResponse.result.hasPriorDebt) {
          if (!isPageEditing) {
            dispatch(setPriorDebtInfo(enrollmentData.createCustomerResponse.result.priorDebt));
          }
          const data = enrollmentData.createCustomerResponse?.result?.priorDebt?.PriorDebt.some(
            (item) => item?.TotalDebt > 0
          );
          if (data) {
            const PriorDebtLink = isMovinUrlUpdate
              ? props.fields.MviPlaceOrderPriorDebtPageLink.value.href
              : props.fields.PlaceOrderPriorDebtPageLink.value.href;
            router.push({
              pathname: PriorDebtLink,
              query: {
                ...router.query,
                ...commonParams,
              },
            });
          }

          // if (
          //   enrollmentData.createCustomerResponse.result.priorDebtStatus ===
          //   'Prior_Debt_Above_Threshold'
          // ) {
          //   const PriorDebtLink = isMovinUrlUpdate
          //     ? props.fields.MviPlaceOrderPriorDebtPageLink.value.href
          //     : props.fields.PlaceOrderPriorDebtPageLink.value.href;
          //   router.push({
          //     pathname: PriorDebtLink,
          //     query: {
          //       ...router.query,
          //       ...commonParams,
          //     },
          //   });
          // } else if (
          //   enrollmentData.createCustomerResponse.result.priorDebtStatus ===
          //   'Prior_Debt_Below_Threshold'
          // ) {
          //   if (enrollmentData.checkSecurityDepositResponse.result.isDepositRequired === false) {
          //     const noDepositLink = isMovinUrlUpdate
          //       ? props.fields.MviPlaceOrderPageLink.value.href
          //       : props.fields.PlaceOrderPageLink.value.href;

          //     if (noDepositLink) {
          //       router.push({
          //         pathname: noDepositLink,
          //         query: {
          //           ...router.query,
          //           ...commonParams,
          //         },
          //       });
          //     }
          //   } else if (
          //     enrollmentData.checkSecurityDepositResponse.result.isDepositRequired === true &&
          //     enrollmentData.calculateDepositResponse.result > 0 // TODO: Should be removed once SAP returns proper deposit amount if isDepositRequired = true
          //   ) {
          //     const PriorDebtLink = isMovinUrlUpdate
          //       ? props.fields.MviPlaceOrderDepositPageLink.value.href
          //       : props.fields.PlaceOrderDepositPageLink.value.href;
          //     router.push({
          //       pathname: PriorDebtLink,
          //       query: {
          //         ...router.query,
          //         ...commonParams,
          //       },
          //     });
          //   } else {
          //     const PriorDebtLink = isMovinUrlUpdate
          //       ? props.fields.MviPlaceOrderPageLink.value.href
          //       : props.fields.PlaceOrderPageLink.value.href;
          //     router.push({
          //       pathname: PriorDebtLink,
          //       query: {
          //         ...router.query,
          //         ...commonParams,
          //       },
          //     });
          //   }
          // }
        } else if (
          enrollmentData.checkSecurityDepositResponse.result.isDepositRequired &&
          enrollmentData.calculateDepositResponse.result > 0 // TODO: Should be removed once SAP returns proper deposit amount if isDepositRequired = true
        ) {
          const MoveInLink = isMovinUrlUpdate
            ? props.fields.MviPlaceOrderDepositPageLink.value.href
            : props.fields.PlaceOrderDepositPageLink.value.href;
          router.push({
            pathname: MoveInLink,
            query: {
              ...router.query,
              ...commonParams,
              isDepositRequired:
                enrollmentData.checkSecurityDepositResponse.result.isDepositRequired,
            },
          });
        } else {
          const MoveInLink = isMovinUrlUpdate
            ? props.fields.MviPlaceOrderPageLink.value.href
            : props.fields.PlaceOrderPageLink.value.href;
          if (MoveInLink)
            router.push({
              pathname: MoveInLink,
              query: {
                ...router.query,
                ...commonParams,
              },
            });
        }
      } else {
        if (
          enrollmentData.connectValidateResponse.messages &&
          enrollmentData.connectValidateResponse.messages[0] == 'SwitchHoldPending'
        ) {
          router.push({
            pathname: props.fields.SwitchHoldPageLink.value.href,
            query: {
              errorCode: enrollmentData.connectValidateResponse.messages[0],
            },
          });
        } else {
          router.push({
            pathname: '/oops',
            query: {
              errorCode: enrollmentData.connectValidateResponse.result.indicator,
            },
          });
        }
      }
    } catch (error) {
      const err = error as AxiosError;
      logErrorToAppInsights(err, {
        componentStack: 'OrderInformationContainer-CreateCustomer',
      });
      //console.log(err.toJSON());
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
        },
      });
      return;
    }
  };

  const onsubmit = async () => {
    form.validate();
    if (form.isValid()) {
      if (props.fields.EnableDRS.value) token = await onSubmitDRSScriptEvent();
      disableSubmit();
      customerInfoUpdate();

      //TD validation

      const fraudServiceFlag = props.fields.EnableFraudService.value;
      if (fraudServiceFlag) {
        const fraudCheck = await FraudCheck();
        if (fraudCheck.toLowerCase() === 'success') {
          if (isCustomerValid) {
            await createCustomer('', false, token);
          } else {
            enableSubmit();
            setShowMismatchModal(true);
          }
        } else if (fraudCheck.toLowerCase() === 'oow' && isCustomerValid) {
          ValidateOOW();
        } else if (fraudCheck === 'fraudcheckfails') {
          router.push({
            pathname: props.fields.GeneralOopsRedirectLink.value.href,
            query: {
              ...router.query,
              errorCode: 'EmailOrPhoneInvalid',
              td: true,
            },
          });
        } else {
          router.push({
            pathname: props.fields.FraudMatchRedirectLink.value.href,
            query: {
              ...router.query,
            },
          });
        }
      } else {
        const tdFraudCheck = await CheckTDandFraudCheck();

        if (tdFraudCheck === 'Success') {
          if (IsOOWEnabled && isCustomerValid) {
            ValidateOOW();
          } else if (isCustomerValid) {
            await createCustomer('', false, token);
          } else {
            enableSubmit();
            setShowMismatchModal(true);
          }
        } else if (tdFraudCheck === 'tdfails') {
          router.push({
            pathname: props.fields.GeneralOopsRedirectLink.value.href,
            query: {
              ...router.query,
              errorCode: 'EmailOrPhoneInvalid',
              td: true,
            },
          });
        } else if (tdFraudCheck === 'fraudmatchtrue') {
          router.push({
            pathname: props.fields.FraudMatchRedirectLink.value.href,
            query: {
              ...router.query,
            },
          });
        } else {
          router.push({
            pathname: props.fields.GeneralOopsRedirectLink.value.href,
            query: {
              ...router.query,
            },
          });
        }
      }
    } else {
      onErrorFocus();
    }
  };

  return (
    <div className="w-full flex flex-col sm:flex-row justify-center gap-5  4xl:gap-[60px] sm:mt-12 wide:flex-col ipad:flex-col wide:gap-[10px] ipad:gap-[10px]">
      <div>
        <Modal
          size="800px"
          opened={isSwitchToMoveIn}
          onClose={() => {
            router.push({
              pathname: props.fields.SwitchToMoveCloseLink.value.href,
            });
            return;
          }}
          closeOnClickOutside={false}
          closeOnEscape={false}
          withCloseButton={true}
          styles={{
            body: {
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '100px',
            },
            root: {},
            close: {
              color: '#0075DA',
            },
          }}
          centered
        >
          <Placeholder
            name="swi-popup"
            rendering={props.rendering}
            form={form}
            isSwitchToMoveIn={isSwitchToMoveIn}
            setisSwitchToMoveIn={setisSwitchToMoveIn}
            continueSubmit={continueSubmit}
          />
        </Modal>
        <form className="w-full ">
          <Placeholder
            rendering={props.rendering}
            name="jss-orderinfo"
            form={form}
            showModal={showModal}
            setShowMismatchModal={setShowMismatchModal}
            setFocusOnContinue={setFocusOnContinue}
            setIsCustomerValid={setIsCustomerValid}
            createCustomer={createCustomer}
            isFocused={isFocused}
            render={(components) => {
              return (
                <div className="flex flex-col w-full mb-[60px] sm:mb-[80px] sm:items-center mt-4 sm:mt-0">
                  {components.map((component, index) => {
                    return (
                      <div
                        key={index}
                        className={`flex flex-col w-full  items-start ${
                          index >= 2 ? ' sm:mt-[40px] mt-0' : ''
                        }`}
                      >
                        {component}
                      </div>
                    );
                  })}
                  <Placeholder
                    name={'jss-credit-check'}
                    rendering={props.rendering}
                    form={form}
                    render={(components) => (
                      <div
                        id="rightside-container"
                        className="w-full sm:w-[800px] flex justify-center sm:justify-start sm:mt-0 mt-6 px-6 sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]"
                      >
                        {components}
                      </div>
                    )}
                    updateSchema={updateSchema}
                  />
                  <div className="w-full sm:w-[800px] flex justify-center sm:justify-start wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px] sm:mt-[-20px]">
                    <Button
                      type="button"
                      className="w-[335px] sm:w-[176px]  rvFormCntuBtn"
                      onBlur={() => setFocusOnContinue(false)}
                      showLoader={form.isValid() && isCustomerValid}
                      onClick={onsubmit}
                      disabled={submitDisabled}
                    >
                      {props.fields.ContinueButtonLabel.value}
                    </Button>
                  </div>
                </div>
              );
            }}
          />
        </form>
      </div>
      <div className="block w-full max-w-full  sm:max-w-[400px] -mt-[2rem]  mb-10 sm:mb-0 sm:-mt-0">
        {props.rendering && (
          <Placeholder
            name="jss-rightside-section"
            rendering={props.rendering}
            render={(components) => (
              <div
                id="rightside-container"
                className="w-full flex flex-col items-center sm:items-end gap-[10px] wide:items-center wide:mt-[30px] ipad:items-center ipad:mt-[30px] px-6 sm:px-0 ipad:px-6 wide:px-6"
              >
                {components}
              </div>
            )}
          />
        )}
      </div>
    </div>
  );
};

const Component = withDatasourceCheck()<OrderInformationContainerProps>(OrderInformationContainer);
export default aiLogger(Component, Component.name);

export const getServerSideProps: GetServerSideComponentProps = async (
  _rendering,
  _layoutData,
  context
) => {
  try {
    const { rdcampaign } = context.query as QueryParamsMapType;
    const isRdCampaign = rdcampaign === 'true';
    const access_token = context.req.session.user?.access_token;

    if (isRdCampaign && access_token) {
      const { cint, dwel, tdsp, planid, vendorid } = context.query as QueryParamsMapType;

      const offerBody = {
        customerIntent: CustomerIntent[cint],
        promoCode: context.query.prom as string,
        dwellingType: DwellingType[dwel],
        postalCode: parseInt(context.query.zip as string),
        language: localeMap[context.locale as string],
        channel: 'Web',
        tdsp: tdsp.split('_', 2)[1],
        vendorId: vendorid === null || vendorid === undefined || vendorid === '' ? 'WEB' : vendorid,
      };

      const response = await getPlans(offerBody, access_token, context.locale as string);

      const offers = response && response.plans.result.offers;
      const plan = offers && offers.filter((offer) => offer.id.includes(planid))[0];

      if (plan) {
        const planContent = response;
        return { plan, planContent, correlationid: response?.correlationid };
      } else {
        const parsedUrlParams = encode(context.query);
        if (cint === '4') {
          return {
            redirect: '/mvi/offers?' + parsedUrlParams,
          };
        } else {
          return {
            redirect: '/swi/offers?' + parsedUrlParams,
          };
        }
      }
    } else return null;
  } catch (error: unknown) {
    const err = error as AxiosError;
    console.error(err);
    return null;
  }
};
