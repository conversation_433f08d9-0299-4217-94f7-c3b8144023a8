import { useForm, zodResolver } from '@mantine/form';
import {
  Field,
  LinkField,
  Placeholder,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios from 'axios';
import { SelectedItemType } from 'components/AddressTypeAhead/AddressTypeAhead';
import Button from 'components/Elements/Button/Button';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useLoader } from 'src/hooks/modalhooks';
import { setSelectedAddress } from 'src/stores/addSlice';
import { clearAddonPlans } from 'src/stores/planSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { z } from 'zod';

type P2PContainerProps = ComponentProps & {
  fields: {
    ContinueButtonText: Field<string>;
    MoveInRedirectLink: LinkField;
    SwitchRedirectLink: <PERSON>Field;
    ActiveESIIDRedirectionLink: LinkField;
    AddressErrorMessage: Field<string>;
    IntentErrorMessage: Field<string>;
    DwelErrorMessage: Field<string>;
  };
};

export interface P2PFormType extends SelectedItemType {
  intent: string;
  dwel: string;
}

// const PendingTransactionStatus = async (esiid: string) => {
//   const response = await axios.get('/api/myaccount/transfer/pendingtransactionstatus', {
//     params: {
//       esiid: esiid,
//     },
//   });
//   return response?.data?.result;
// };

const P2PContainer = (props: P2PContainerProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let dispatch: ReturnType<typeof useAppDispatch>;
  const router = useRouter();
  const { openModal } = useLoader();
  let bpNumber = undefined;
  if (!isPageEditing) {
    bpNumber = useAppSelector((state) => state.authuser?.bpNumber);
    dispatch = useAppDispatch();
  }
  const onsubmit = async () => {
    form.validate();
    if (form.isValid()) {
      openModal();
      // if (form.values.esiid !== '') {
      //   const NewAddressESIIDStatus = await PendingTransactionStatus(form.values.esiid);
      //   const NewAddressPartnerNumber =
      //     NewAddressESIIDStatus?.partnerNumber?.length === 8
      //       ? '00' + NewAddressESIIDStatus?.partnerNumber
      //       : NewAddressESIIDStatus?.partnerNumber;
      //   if (NewAddressESIIDStatus?.isActive && NewAddressPartnerNumber !== bpNumber) {
      //     router.push({
      //       pathname: props?.fields?.ActiveESIIDRedirectionLink?.value?.href,
      //       query: {
      //         cint: form.values.intent,
      //         dwel: form.values.dwel,
      //         prom: form.values.dwel === '02' ? 'PM' : 'PS',
      //         zip: form.values.zip,
      //         esiid: form.values.esiid,
      //         tdsp: form.values.tdsp,
      //       },
      //     });
      //     return;
      //   }
      // }
      if (!isPageEditing) {
        dispatch(
          setSelectedAddress({
            label: form.values.label,
            esiid: form.values.esiid,
            value: form.values.value,
            city: form.values.city,
            state: form.values.state,
            zip: form.values.zip,
            street: form.values.street,
            house_nbr: form.values.house_nbr,
            tdsp: form.values.tdsp,
            display_text: form.values.display_text,
            unit: form.values.unit,
          })
        );
      }
      if (form.values.esiid !== '') {
        router.push({
          pathname:
            form.values.intent === '6'
              ? props?.fields?.MoveInRedirectLink?.value?.href
              : props?.fields?.SwitchRedirectLink?.value?.href,
          query: {
            cint: form.values.intent,
            dwel: form.values.dwel,
            prom: form.values.dwel === '02' ? 'PM' : 'PS',
            zip: form.values.zip,
            esiid: form.values.esiid,
            tdsp: form.values.tdsp,
          },
        });
      }
    }
  };

  const customerInfoSchema = z.object({
    intent: z.string().nonempty({ message: props?.fields?.IntentErrorMessage?.value }),
    dwel: z.string().nonempty({ message: props?.fields?.DwelErrorMessage?.value }),
  });

  const serviceaddressInfoSchema = z.object({
    zip: z.string().nonempty({ message: props?.fields?.AddressErrorMessage?.value }),
    esiid: z.string().optional(),
  });

  const finalSchema = customerInfoSchema.and(serviceaddressInfoSchema);

  const form = useForm<P2PFormType>({
    initialValues: {
      intent: '',
      dwel: '',
      tdsp: '',
      esiid: '',
      zip: '',
      house_nbr: '',
      street: '',
      state: '',
      label: '',
      value: '',
      city: '',
      display_text: '',
      unit: '',
    },
    validate: zodResolver(finalSchema),
    validateInputOnChange: true,
    validateInputOnBlur: true,
  });

  useEffect(() => {
    form.clearErrors();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.locale as string]);

  useEffect(() => {
    if (!isPageEditing) {
      dispatch(clearAddonPlans());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-col justify-start ml-0 sm:mr-auto sm:ml-[410px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6">
      <form>
        <Placeholder
          name="jss-P2P"
          rendering={props.rendering}
          form={form}
          render={(components) => {
            return (
              <div className="">
                {components.map((component, index) => {
                  return (
                    <div
                      key={index}
                      className={`flex flex-col w-full ${
                        index >= 2 ? 'mt-[32px] sm:mt-[40px]' : ''
                      }`}
                    >
                      {component}
                    </div>
                  );
                })}
              </div>
            );
          }}
        />
        <div className="max-w-[832px]  sm:px-0 px-6 my-4 mb-16">
          <Button
            type="button"
            className="sm:min-w-[245px] sm:w-auto h-[56px] min-w-[170px] w-auto px-4 gap-0 m-auto sm:m-0"
            onClick={onsubmit}
          >
            {props.fields.ContinueButtonText.value}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default P2PContainer;
