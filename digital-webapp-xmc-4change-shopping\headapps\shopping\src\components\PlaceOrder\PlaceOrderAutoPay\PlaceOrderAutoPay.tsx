import { withDatasourceCheck, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { Group, Radio } from '@mantine/core';
import { Text } from '@sitecore-jss/sitecore-jss-react';
import { PlaceOrderAutoPayProps } from './componentprops/PlaceOrderAutoPay.types';
import Divider from 'components/common/Divider/Divider';
import { useEffect, useState } from 'react';
import AutoPayCard from './elements/AutoPayCard';
import PaymetricIntegration from 'components/PaymetricIntegration/PaymetricIntegration';
import { AutoPayDecision } from './utils/AutoPayDecisionFunction';
import { useAppSelector } from 'src/stores/store';

const PlaceOrderAutoPay = (props: PlaceOrderAutoPayProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let isAutoPayEligible = undefined;
  let DepositValueStore = undefined;
  if (!isPageEditing) {
    isAutoPayEligible = useAppSelector((state) => state.enrollment?.autopayEligible);
    DepositValueStore = useAppSelector((state) => state.enrollment?.isDepositRequired);
  }
  const [selectedOption, setSelectedOption] = useState<
    'autoPay' | 'selfPay' | 'samePaymentMethod' | null
  >(null);
  const [showingContent, setShowingContent] = useState({});

  useEffect(() => {
    (async () => {
      const decisionShowingValue = await AutoPayDecision();
      setShowingContent(decisionShowingValue);
    })();
  }, []);

  // Set initial selected option based on decision
  useEffect(() => {
    if (showingContent?.defaultSelectedAutoPay === true) {
      props.onAutoPaysSelection?.(true);
      setSelectedOption('autoPay');
    } else if (showingContent?.defaultSelectedAutoPay === false) {
      props.onAutoPaysSelection?.(false);
      setSelectedOption('selfPay');
    }
  }, [showingContent]);

  const selectAutoPay = () => {
    props.onAutoPaysSelection?.(true);
    setSelectedOption('autoPay');
  };

  const selectSelfPay = () => {
    props.onAutoPaysSelection?.(false);
    setSelectedOption('selfPay');
  };

  const selectSamePaymentMethod = () => {
    // props.onAutoPaysSelection(true); // If this also enables autopay
    setSelectedOption('samePaymentMethod');
  };
  return (
    <div className="terms-section flex flex-col gap-5 md:gap-8 md:w-[830px] px-[15px] wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
      <div>
        {!DepositValueStore && (
          <>
            <Text
              tag="p"
              className={`text-minus1 text-textQuattuordenary font-primaryBold `}
              field={{
                value: props.fields?.NoDepositTitle?.value,
              }}
            />
            <Text
              tag="p"
              className={`text-plus2 text-textQuattuordenary font-primaryBold `}
              field={{
                value: props.fields?.PaymentMethodTitle?.value,
              }}
            />

            <div className="sm:flex gap-4 mb-[20px]">
              <Radio.Group
                value={selectedOption}
                onChange={(val) => setSelectedOption(val as any)}
                className=" text-[24px] font-primaryBold"
              >
                <Group className={'flex flex-col items-start'} mt={15}>
                  <Radio
                    value="autoPay"
                    onClick={selectAutoPay}
                    styles={{
                      root: {
                        marginTop: '10px',
                      },
                    }}
                    label={
                      <span
                        className="text-[16px] leading-[22px]"
                        dangerouslySetInnerHTML={{
                          __html: props.fields.AutoPayCheckboxLabel?.value,
                        }}
                      />
                    }
                  />
                  {showingContent?.showSelfPay && (
                    <Radio
                      value="selfPay"
                      onClick={selectSelfPay}
                      styles={{
                        root: {
                          marginTop: '10px',
                        },
                      }}
                      label={
                        <span
                          className="text-[16px] leading-[22px]"
                          dangerouslySetInnerHTML={{
                            __html: props.fields.SelfPayCheckboxLabel?.value,
                          }}
                        />
                      }
                    />
                  )}
                </Group>
              </Radio.Group>
            </div>
          </>
        )}

        <Divider />
        {DepositValueStore && isAutoPayEligible && (
          <>
            <Text
              tag="p"
              className={`text-plus2 text-textQuattuordenary font-primaryBold py-2`}
              field={{
                value: props.fields?.AutoPayMethodTitle?.value,
              }}
            />
            <Group className={'flex flex-col items-start mt-4 mb-4 sm:mt-4 sm:mb-0'}>
              <Radio
                value="samePaymentMethod"
                onClick={selectSamePaymentMethod}
                checked={true}
                label={
                  <span
                    className="text-[16px] leading-[22px]"
                    dangerouslySetInnerHTML={{
                      __html: props.fields.SamePaymentMethodCheckboxLabel?.value,
                    }}
                  />
                }
              />
            </Group>
          </>
        )}
        {!DepositValueStore ? (
          selectedOption === 'autoPay' || selectedOption === 'samePaymentMethod' ? (
            <>
              <AutoPayCard
                AutoPayMethodTitle={props.fields?.AutoPayMethodTitle?.value}
                AutoPayHelpText={props.fields?.AutoPayHelpText?.value}
                AutoPayLinkText={props.fields?.AutoPayLinkText?.value}
                AutoPayDescription={props.fields?.AutoPayDescription?.value}
                AutoPayLinkPopupText={props.fields?.AutoPayLinkPopupText?.value}
              />
              <PaymetricIntegration
                accessToken={props.accessToken}
                setAccessToken={props.setAccessToken}
              />
            </>
          ) : null
        ) : null}
      </div>
    </div>
  );
};

export { PlaceOrderAutoPay };
const Component = withDatasourceCheck()<PlaceOrderAutoPayProps>(PlaceOrderAutoPay);
export default aiLogger(Component, Component.name);
