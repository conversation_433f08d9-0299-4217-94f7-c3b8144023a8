/* eslint-disable @typescript-eslint/no-explicit-any */
import { useForm, zodResolver } from '@mantine/form';
import { z } from 'zod';
import { useDisclosure } from '@mantine/hooks';
import {
  Field,
  LinkField,
  Placeholder,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios, { AxiosError, AxiosResponse } from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { PaymetricResponse } from 'components/PaymetricIntegration/PaymetricIntegration';
import dayjs from 'dayjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import usePreventAbandon from 'src/hooks/usePreventAbandon';
import { PaymentDepositResponse, PaySecurityDepositBody } from 'src/services/EnrollmentAPI/types';
import {
  setAutoPay,
  setAutoPayFailure,
  setPaymentInfo,
  setServiceAccountNumber,
} from 'src/stores/enrollmentSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { connect } from 'src/utils/connectCall';
import {
  getPlaceOrderSchema,
  PlaceOrderErrorMessages,
  getPlaceOrderFormInitialValues,
} from 'src/utils/placeOrderForm';
import handleSubmit from 'src/utils/placeOrderSubmit';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';
import {
  OrderAddonProductRequest,
  OrderAddonProductResponse,
  OrderedAddonProduct,
} from 'src/services/AddOnProductAPI/types';
import { DwellingType } from 'src/utils/query-params-mapping';
import { setOrderedPlans } from 'src/stores/planSlice';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { decryptURL, getANumber } from 'src/utils/util';
interface PlaceOrderContainerProps extends ComponentProps {
  fields: {
    AcceptAuthorizatioErrorText: Field<string>;
    TermsOfServiceErrorText: Field<string>;
    LegalAuthorizationErrorText: Field<string>;
    PlaceOrderButtonLabel: Field<string>;
    MiniConfirmationPageLink: LinkField;
    AddonPlanID: Field<string>;
  };
}

const PlaceOrderContainer = (props: PlaceOrderContainerProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let dispatch: ReturnType<typeof useAppDispatch>;
  const router = useRouter();
  const { dwel, cint, prom, cnumber, anumber, web_experienceid, vendorid } =
    router.query as QueryParamsMapType;
  let enrollment = '';
  let selectedPlan = '';
  let addOnPlan = undefined;
  let solarAdded = undefined;
  let evDisclaimer = undefined;
  let AutoPayEnableStore = '';
  let isDepositRequired = '';
  let isRenewableEnergy = undefined;
  if (!isPageEditing) {
    enrollment = useAppSelector((state) => state.enrollment);
    selectedPlan = useAppSelector((state) => state.plans?.selectedPlan);
    addOnPlan = useAppSelector((state) => state.plans?.addonPlans);
    solarAdded = useAppSelector((state) => state.plans?.solarAdded);
    evDisclaimer = useAppSelector((state) => state.plans?.EVDisclaimer);
    AutoPayEnableStore = useAppSelector((state) => state.enrollment?.autopayEligible);
    isDepositRequired = useAppSelector((state) => state.enrollment?.isDepositRequired);
    isRenewableEnergy = useAppSelector((state) => state.enrollment.isRenewableEnergy);
    dispatch = useAppDispatch();
  }
  const priorDebt = enrollment?.priorDebtInfo?.PriorDebt;
  const isPriorDebt = priorDebt?.length > 0;
  let totalPriorBalance = 0;
  if (isPriorDebt) {
    totalPriorBalance = priorDebt?.reduce((a, v) => (a = a + v.TotalDebt), 0);
  }
  const [isPayPriorDebtNow, setIsPayPriorDebtNow] = useState(false);
  const [accessToken, setAccessToken] = useState<string>('');
  const [submitDisabled, { open: disableSubmit }] = useDisclosure(false);
  const [isAutopaySelected, setAutopaySelected] = useState<boolean>(false);

  const allowedUrls: (string | undefined)[] = [
    props.fields.MiniConfirmationPageLink.value.href,
    '/oops',
  ];
  usePreventAbandon(allowedUrls);
  1;
  const { openModal: openLoaderModal } = useLoader();

  const disclaimerMessges: PlaceOrderErrorMessages = {
    acceptAuthorizationError: props.fields.AcceptAuthorizatioErrorText.value,
    legalAuthorizationError: props.fields.LegalAuthorizationErrorText.value,
    termsOfServiceError: props.fields.TermsOfServiceErrorText.value,
    evError: evDisclaimer?.DiclaimerErrorText,
  };
  const isEV = evDisclaimer?.EVModels?.includes(selectedPlan.ev);
  const baseSchema = getPlaceOrderSchema(disclaimerMessges);
  const baseInitialValues = getPlaceOrderFormInitialValues(isEV === undefined ? false : isEV);

  const baseZodSchema =
    baseSchema instanceof z.ZodDiscriminatedUnion
      ? baseSchema
      : z.discriminatedUnion('isEVPlan', [
          z.object({ isEVPlan: z.literal(false) }),
          z.object({ isEVPlan: z.literal(true) }),
        ]);

  const [formConfig, setFormConfig] = useState({
    initialValues: baseInitialValues,
    validationSchema: baseZodSchema,
  });

  const form = useForm({
    initialValues: formConfig.initialValues,
    validate: zodResolver(formConfig.validationSchema),
    validateInputOnChange: true,
    validateInputOnBlur: true,
  });

  function getCNumber() {
    const value = decryptURL(cnumber);
    const cno = value?.toString();
    if (cno !== undefined && cno !== '') {
      return cno.startsWith('c', 0) ? cno.replace(cno[0], 'C') : 'C' + cno;
    } else {
      return '';
    }
  }

  const getSecurityDeposit = async (resPacket: PaymetricResponse): Promise<void> => {
    openLoaderModal();
    disableSubmit();
    const cardDetails = {
      cardToken: resPacket?.message?.Fields?.FormField[3]?.Value,
      CVV: resPacket?.message?.Fields?.FormField[5]?.Value,
      expDate: new Date(
        parseInt(resPacket?.message?.Fields?.FormField[2]?.Value),
        parseInt(resPacket?.message?.Fields?.FormField[1]?.Value),
        0
      ).toISOString(),
      cardHolder: resPacket?.message?.Fields?.FormField[4]?.Value,
    };
    const bankDetails = null;
    const connectCall = await connect(
      enrollment,
      cint,
      dwel,
      prom,
      selectedPlan,
      getCNumber(),
      getANumber(anumber),
      web_experienceid,
      enrollment?.autopayEligible,
      isDepositRequired,
      cardDetails,
      bankDetails,
      vendorid
    );
    if (connectCall && connectCall.result.indicator === 'Success') {
      if (!isPageEditing) {
        dispatch(setServiceAccountNumber(connectCall.result.serviceContractNumber));
      }
      if (cardDetails !== null || bankDetails !== null) {
        if (!isPageEditing) {
          dispatch(setAutoPay(true));
        }
      }
      let cardType = resPacket.message.Fields.FormField[0].Value;
      if (cardType === 'vi') {
        cardType = 'Visa';
      } else if (cardType === 'mc') {
        cardType = 'Mastercard';
      } else {
        cardType = 'Credit';
      }

      const req = await axios.post<
        PaymentDepositResponse,
        AxiosResponse<PaymentDepositResponse, PaySecurityDepositBody>,
        PaySecurityDepositBody
      >('/api/payments/paymetric/securitydeposit', {
        expiration: new Date(
          parseInt(resPacket.message.Fields.FormField[2].Value),
          parseInt(resPacket.message.Fields.FormField[1].Value),
          0
        ).toISOString(),
        contractAccountNumber: enrollment.enrollmentInfo.contractAccountNumber,
        language:
          enrollment.customerInfo.correspondanceLanguage === 'Español'
            ? 'Spanish'
            : enrollment.customerInfo.correspondanceLanguage,
        paymentDate: dayjs().toString(),
        cvv: `${resPacket.message.Fields.FormField[5].Value}`,
        billingPostalCode: `${resPacket.message.Fields.FormField[6].Value}`,
        cardType: 'Credit',
        payChannel: '',
        profileId: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
        payAgent: '',
        tenderType: cardType,
        paymentType: 'Card',
        nickname: resPacket.message.Fields.FormField[4].Value,
        holderName: resPacket.message.Fields.FormField[4].Value,
        accountId: resPacket.message.Fields.FormField[3].Value,
        serviceContractNumber: enrollment.enrollmentInfo.contractAccountNumber,
        bpNumber: enrollment.enrollmentInfo.bpNumber,
        amount: totalPriorBalance.toString(),
        store: enrollment?.autopayEligible ?? false,
        autoPayEligible: enrollment?.autopayEligible ?? false,
      });
      if (!isPageEditing) {
        dispatch(setAutoPayFailure(req.data.result.autopayFailure));
      }
      const paymentInfo = {
        paymentMethod: cardType,
        priorDebtPaid: isPayPriorDebtNow,
        paymentType: 'Card',
        cardHolderName: resPacket.message.Fields.FormField[4].Value,
        cardNumber: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
        expirationDate: new Date(
          parseInt(resPacket.message.Fields.FormField[2].Value),
          parseInt(resPacket.message.Fields.FormField[1].Value),
          0
        ).toISOString(),
        zipCode: resPacket.message.Fields.FormField[6].Value,
        scheduledDepositDate: enrollment.paymentInfo.scheduledDepositDate,
      };
      if (!isPageEditing) {
        dispatch(setPaymentInfo(paymentInfo));
      }
      console.log('Security Payment Response:', JSON.stringify(req, null, 4));

      if (req.data.result.indicator !== 'Success') {
        router.push({
          pathname: '/oops',
          query: {
            ...router.query,
          },
        });
      } else {
        if (cardDetails !== null || bankDetails !== null) {
          if (!isPageEditing) {
            dispatch(setAutoPay(true));
          }
        }
        router.push({
          pathname: props.fields.MiniConfirmationPageLink.value.href,
          query: { ...router.query },
        });
      }
    } else {
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
          errorCode: connectCall && connectCall.result.indicator,
        },
      });
    }
  };

  async function placeOrderSubmit() {
    form.validate();
    if (form.isValid()) {
      if (isPayPriorDebtNow) {
        handleSubmit(getSecurityDeposit, accessToken);
      } else if (AutoPayEnableStore || isAutopaySelected) {
        handleSubmit(handleConnectCall, accessToken);
      } else {
        handleConnectCall();
      }
    }
  }

  async function handleConnectCall(resPacket?: PaymetricResponse) {
    openLoaderModal();
    disableSubmit();
    let cardDetails;
    let autopayEligible;
    if (resPacket && (AutoPayEnableStore || isAutopaySelected)) {
      cardDetails = {
        cardToken: resPacket?.message?.Fields?.FormField[3]?.Value,
        CVV: resPacket?.message?.Fields?.FormField[5]?.Value,
        expDate: new Date(
          parseInt(resPacket?.message?.Fields?.FormField[2]?.Value),
          parseInt(resPacket?.message?.Fields?.FormField[1]?.Value),
          0
        ).toISOString(),
        cardHolder: resPacket?.message?.Fields?.FormField[4]?.Value,
      };
      autopayEligible = true;
    } else {
      autopayEligible = false;
      cardDetails = null;
    }

    const bankDetails = null;
    const connectCall = await connect(
      enrollment,
      cint,
      dwel,
      prom,
      selectedPlan,
      getCNumber(),
      getANumber(anumber),
      web_experienceid,
      autopayEligible,
      isDepositRequired,
      cardDetails,
      bankDetails,
      vendorid
    );
    if (connectCall && connectCall.result.indicator === 'Success') {
      if (cardDetails !== null || bankDetails !== null) {
        if (!isPageEditing) {
          dispatch(setAutoPay(true));
        }
      }
      if (!isPageEditing) {
        dispatch(setServiceAccountNumber(connectCall.result.serviceContractNumber));
      }
      await orderAddonProducts();
      router.push({
        pathname: props.fields.MiniConfirmationPageLink.value.href,
        query: { ...router.query },
      });
    } else {
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
          errorCode: connectCall && connectCall.result.indicator,
        },
      });
    }
  }

  const orderAddonProducts = async () => {
    if (!isRenewableEnergy) return;
    try {
      const orderAddonReq = await axios.post<
        OrderAddonProductResponse,
        AxiosResponse<OrderAddonProductResponse>,
        OrderAddonProductRequest
      >(
        '/api/plans/addonplans',
        {
          businessPartnerNumber: enrollment.enrollmentInfo.bpNumber,
          contractAccountNumber: enrollment.enrollmentInfo.contractAccountNumber,
          esiid: enrollment.serviceInfo.esiid,
          zipCode: enrollment.serviceInfo.postalCode,
          currentPlanId: selectedPlan.planId,
          dwellingType: DwellingType[dwel as string],
          language:
            enrollment.customerInfo.correspondanceLanguage === 'Español'
              ? 'Spanish'
              : enrollment.customerInfo.correspondanceLanguage,
          productOrders: [
            {
              productId: props.fields?.AddonPlanID?.value,
              quantity: 1,
            },
          ],
        },
        { headers: { 'Content-Type': 'application/json' } }
      );
      console.log(orderAddonReq);
      if (!isPageEditing) {
        dispatch(setOrderedPlans(orderAddonReq.data.result));
      }
    } catch (err) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'MiniConfirmationContainer-orderAddonProducts',
      });
      console.log(error);
    }
  };

  const updateSchema = (newFields: Record<string, any>, newSchema: z.ZodObject<any>) => {
    setFormConfig((prevConfig) => ({
      initialValues: { ...prevConfig.initialValues, ...newFields },
      validationSchema: z.discriminatedUnion(
        prevConfig.validationSchema.discriminator,
        prevConfig.validationSchema.options.map((schema: any) => schema.merge(newSchema))
      ),
    }));
  };

  return (
    <div className="w-full flex flex-col sm:flex-row justify-center gap-[10px] sm:gap-5 4xl:gap-[60px] sm:mt-12 wide:flex-col ipad:flex-col wide:gap-[10px] ipad:gap-[10px]">
      <div className="">
        <form className="w-full">
          <Placeholder
            rendering={props.rendering}
            name="jss-placeorder"
            isPayPriorDebtNow={isPayPriorDebtNow}
            setIsPayPriorDebtNow={setIsPayPriorDebtNow}
            accessToken={accessToken}
            setAccessToken={setAccessToken}
            form={form}
            updateSchema={updateSchema}
            showReviewTermsTitle={solarAdded}
            onAutoPaysSelection={setAutopaySelected}
            render={(components) => {
              return (
                <div className="flex flex-col w-full mb-[60px] sm:mb-[80px] sm:items-center mt-4 sm:mt-0">
                  {components.map((component, index) => {
                    return (
                      <div
                        key={index}
                        className={`flex flex-col w-full items-center ${
                          index >= 2 ? 'sm:mt-[20px] my-4' : ''
                        }`}
                      >
                        {component}
                      </div>
                    );
                  })}

                  <div className="w-full sm:w-[800px] flex justify-center sm:justify-start mt-8 px-[15px] sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
                    <Button
                      className="w-full h-[56px] md:w-[181px] rvPlaceOrderBtn"
                      type="button"
                      onClick={placeOrderSubmit}
                      // showLoader={form.isValid()}
                      disabled={submitDisabled}
                    >
                      {props.fields.PlaceOrderButtonLabel.value}{' '}
                    </Button>
                  </div>
                </div>
              );
            }}
          />
        </form>
      </div>
      <div className="w-full max-w-full sm:max-w-[400px] -mt-[4rem]  sm:-mt-0">
        {props.rendering && (
          <Placeholder
            name="jss-rightside-section"
            rendering={props.rendering}
            displayWithContactDetails={true}
            render={(components) => (
              <div
                id="rightside-container"
                className="w-full flex flex-col items-center sm:items-end gap-[10px] wide:items-center wide:mt-[30px] ipad:items-center ipad:mt-[30px] px-6 sm:px-3 ipad:px-6 wide:px-6"
              >
                {components}
              </div>
            )}
          />
        )}

        <div className="w-full sm:w-[800px] flex sm:hidden justify-center sm:justify-start mt-4 mb-8 px-[15px] sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
          <Button
            className="w-full h-[56px] md:w-[181px] rvPlaceOrderBtn"
            type="button"
            onClick={placeOrderSubmit}
            // showLoader={form.isValid()}
            disabled={submitDisabled}
          >
            {props.fields.PlaceOrderButtonLabel.value}
          </Button>
        </div>
      </div>
    </div>
  );
};

const Component = withDatasourceCheck()<PlaceOrderContainerProps>(PlaceOrderContainer);
export default aiLogger(Component, Component.name);
