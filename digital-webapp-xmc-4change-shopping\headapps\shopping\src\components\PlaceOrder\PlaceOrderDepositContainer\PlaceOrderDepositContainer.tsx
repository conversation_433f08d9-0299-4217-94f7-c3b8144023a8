import { useForm, zodResolver } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import {
  Field,
  LinkField,
  Placeholder,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios, { AxiosResponse, AxiosError } from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { PaymetricResponse } from 'components/PaymetricIntegration/PaymetricIntegration';
import dayjs from 'dayjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import usePreventAbandon from 'src/hooks/usePreventAbandon';
import {
  PaySecurityDepositBody,
  PaymentDepositResponse,
  ScheduleRemainingDepositBody,
  ScheduleRemainingDepositResponse,
} from 'src/services/EnrollmentAPI/types';
import {
  setAutoPay,
  setAutoPayFailure,
  setPaymentInfo,
  setServiceAccountNumber,
} from 'src/stores/enrollmentSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { connect } from 'src/utils/connectCall';
import {
  PlaceOrderErrorMessages,
  getPlaceOrderFormInitialValues,
  getPlaceOrderSchema,
} from 'src/utils/placeOrderForm';
import handleSubmit from 'src/utils/placeOrderSubmit';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';
import { ActionResponse } from 'src/types/global';
import {
  OrderAddonProductRequest,
  OrderAddonProductResponse,
  OrderedAddonProduct,
} from 'src/services/AddOnProductAPI/types';
import { DwellingType } from 'src/utils/query-params-mapping';
import { setOrderedPlans } from 'src/stores/planSlice';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { z } from 'zod';
import { decryptURL, getANumber } from 'src/utils/util';

type PlaceOrderDepositContainerProps = ComponentProps & {
  fields: {
    AcceptAuthorizatioErrorText: Field<string>;
    TermsOfServiceErrorText: Field<string>;
    LegalAuthorizationErrorText: Field<string>;
    PlaceOrderButtonLabel: Field<string>;
    MiniConfirmationPageLink: LinkField;
    EnableDRS: Field<string>;
    DRSActionName: Field<string>;
    EVErrorText: Field<string>;
    AddonPlanID: Field<string>;
  };
};

const PlaceOrderDepositContainer = (props: PlaceOrderDepositContainerProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let dispatch: ReturnType<typeof useAppDispatch>;
  let correlationId = '';
  let enrollment = undefined;
  let selectedPlan = undefined;
  let addOnPlan = undefined;
  let evDisclaimer = undefined;
  let isDepositRequired = undefined;
  let solarAdded = undefined;
  let isRenewableEnergy = undefined;
  if (!isPageEditing) {
    dispatch = useAppDispatch();
    correlationId = useAppSelector((state) => state.enrollment?.correlationid);
    enrollment = useAppSelector((state) => state.enrollment);
    selectedPlan = useAppSelector((state) => state.plans?.selectedPlan);
    addOnPlan = useAppSelector((state) => state.plans?.addonPlans);
    evDisclaimer = useAppSelector((state) => state.plans?.EVDisclaimer);
    isDepositRequired = useAppSelector((state) => state.enrollment?.isDepositRequired);
    solarAdded = useAppSelector((state) => state.plans.solarAdded);
    isRenewableEnergy = useAppSelector((state) => state.enrollment.isRenewableEnergy);
  }
  const [isPayPriorDebtNow, setIsPayPriorDebtNow] = useState(false);
  const [paymentType, setPaymentType] = useState<string>('0');
  const [accessToken, setAccessToken] = useState<string>('');
  const [isAutopaySelected, setAutopaySelected] = useState<boolean>(false);
  const allowedUrls: (string | undefined)[] = [
    props.fields.MiniConfirmationPageLink.value.href,
    '/oops',
  ];
  const { openModal: openLoaderModal } = useLoader();
  const [submitDisabled, { open: disableSubmit }] = useDisclosure(false);
  usePreventAbandon(allowedUrls);
  const router = useRouter();
  const { dwel, cint, prom, cnumber, anumber, web_experienceid, vendorid } =
    router.query as QueryParamsMapType;
  let priorDebtAmount = 0;
  if (
    enrollment?.priorDebtInfo?.PriorDebt?.length > 0 &&
    !enrollment?.priorDebtInfo.ThresholdPassed
  ) {
    priorDebtAmount = enrollment?.priorDebtInfo?.PriorDebt?.reduce(
      (a, v) => (a = a + v.TotalDebt),
      0
    );
  }

  const depositAmount =
    paymentType === '1' ? enrollment?.depositAmount / 2 : enrollment?.depositAmount;
  const finalDepositAmount = isPayPriorDebtNow
    ? parseFloat((depositAmount + priorDebtAmount).toFixed(2))
    : depositAmount;

  const disclaimerMessges: PlaceOrderErrorMessages = {
    acceptAuthorizationError: props.fields.AcceptAuthorizatioErrorText.value,
    legalAuthorizationError: props.fields.LegalAuthorizationErrorText.value,
    termsOfServiceError: props.fields.TermsOfServiceErrorText.value,
    evError: props.fields.EVErrorText.value,
  };

  let isEV = false;
  if (evDisclaimer != undefined) {
    isEV = evDisclaimer?.EVModels?.includes(selectedPlan?.ev);
  }

  const baseSchema = getPlaceOrderSchema(disclaimerMessges);
  const baseInitialValues = getPlaceOrderFormInitialValues(isEV);

  const baseZodSchema =
    baseSchema instanceof z.ZodDiscriminatedUnion
      ? baseSchema
      : z.discriminatedUnion('isEVPlan', [
          z.object({ isEVPlan: z.literal(false) }),
          z.object({ isEVPlan: z.literal(true) }),
        ]);

  const [formConfig, setFormConfig] = useState({
    initialValues: baseInitialValues,
    validationSchema: baseZodSchema,
  });

  const form = useForm({
    initialValues: formConfig.initialValues,
    validate: zodResolver(formConfig.validationSchema),
    validateInputOnChange: true,
    validateInputOnBlur: true,
  });

  const scheduleRemainingDeposit = async (resPacket: PaymetricResponse): Promise<void> => {
    const req = await axios.post<
      ScheduleRemainingDepositResponse,
      AxiosResponse<ScheduleRemainingDepositResponse, ScheduleRemainingDepositBody>,
      ScheduleRemainingDepositBody
    >('/api/payments/paymetric/scheduleremainingdeposit', {
      PaymentDate: enrollment?.paymentInfo?.scheduledDepositDate,
      ContractAccountNumber: enrollment?.enrollmentInfo?.contractAccountNumber,
      Expiration: new Date(
        parseInt(resPacket.message.Fields.FormField[2].Value),
        parseInt(resPacket.message.Fields.FormField[1].Value),
        0
      ).toISOString(),
      CVV: `${resPacket.message.Fields.FormField[5].Value}`,
      HoldersName: resPacket.message.Fields.FormField[4].Value,
      AccountId: resPacket.message.Fields.FormField[3].Value,
      DisplayAccountNumber: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
      ProfileId: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
      PartnerNumber: enrollment?.enrollmentInfo?.bpNumber,
      Amount: enrollment?.depositAmount / 2,
    });
    console.log('Scheduled API Response', req.data);
    if (req.data.result.indicator !== 'Success') {
      // getSecurityDeposit(resPacket);
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
        },
      });
    }
  };

  function getCNumber() {
    const value = decryptURL(cnumber);
    const cno = value?.toString();
    if (cno !== undefined && cno !== '') {
      return cno.startsWith('c', 0) ? cno.replace(cno[0], 'C') : 'C' + cno;
    } else {
      return '';
    }
  }

  const getSecurityDeposit = async (resPacket: PaymetricResponse): Promise<void> => {
    // const cardDetails = {
    //   cardToken: resPacket?.message?.Fields?.FormField[3]?.Value,
    //   CVV: resPacket.message.Fields.FormField[5].Value,
    //   expDate: new Date(
    //     parseInt(resPacket.message.Fields.FormField[2].Value),
    //     parseInt(resPacket.message.Fields.FormField[1].Value),
    //     0
    //   ).toISOString(),
    //   cardHolder: resPacket.message.Fields.FormField[4].Value,
    // };
    const cardDetails = null;
    const bankDetails = null;
    openLoaderModal();
    disableSubmit();
    const connectCall = await connect(
      enrollment,
      cint,
      dwel,
      prom,
      selectedPlan,
      getCNumber(),
      getANumber(anumber),
      web_experienceid,
      enrollment?.autopayEligible,
      isDepositRequired,
      cardDetails,
      bankDetails,
      vendorid
    );
    if (connectCall && connectCall.result.indicator === 'Success') {
      if (!isPageEditing) {
        dispatch(setServiceAccountNumber(connectCall.result.serviceContractNumber));
      }
      if (cardDetails !== null || bankDetails !== null) {
        if (!isPageEditing) {
          dispatch(setAutoPay(true));
        }
      }
      await orderAddonProducts();
      if (resPacket) {
        let cardType = resPacket.message.Fields.FormField[0].Value;
        if (cardType === 'vi') {
          cardType = 'Visa';
        } else if (cardType === 'mc') {
          cardType = 'Mastercard';
        } else {
          cardType = 'Credit';
        }
        const req = await axios.post<
          PaymentDepositResponse,
          AxiosResponse<PaymentDepositResponse, PaySecurityDepositBody>,
          PaySecurityDepositBody
        >('/api/payments/paymetric/securitydeposit', {
          expiration: new Date(
            parseInt(resPacket.message.Fields.FormField[2].Value),
            parseInt(resPacket.message.Fields.FormField[1].Value),
            0
          ).toISOString(),
          contractAccountNumber: enrollment?.enrollmentInfo?.contractAccountNumber,
          language:
            enrollment?.customerInfo?.correspondanceLanguage === 'Español'
              ? 'Spanish'
              : enrollment?.customerInfo?.correspondanceLanguage,
          paymentDate: dayjs().toString(),
          cvv: `${resPacket.message.Fields.FormField[5].Value}`,
          billingPostalCode: `${resPacket.message.Fields.FormField[6].Value}`,
          cardType: 'Credit',
          payChannel: '',
          profileId: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
          payAgent: '',
          tenderType: cardType,
          paymentType: 'Card',
          nickname: resPacket.message.Fields.FormField[4].Value,
          holderName: resPacket.message.Fields.FormField[4].Value,
          accountId: resPacket.message.Fields.FormField[3].Value,
          serviceContractNumber: enrollment?.enrollmentInfo?.contractAccountNumber,
          bpNumber: enrollment?.enrollmentInfo?.bpNumber,
          amount: finalDepositAmount?.toString(),
          store: enrollment?.autopayEligible ?? false,
          autoPayEligible: enrollment?.autopayEligible ?? false,
        });
        if (!isPageEditing) {
          dispatch(setAutoPay(!req.data.result.autopayFailure));
          dispatch(setAutoPayFailure(req.data.result.autopayFailure));
        }
        const paymentInfo = {
          paymentMethod: cardType,
          priorDebtPaid: isPayPriorDebtNow,
          paymentType: paymentType,
          cardHolderName: resPacket.message.Fields.FormField[4].Value,
          cardNumber: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
          expirationDate: new Date(
            parseInt(resPacket.message.Fields.FormField[2].Value),
            parseInt(resPacket.message.Fields.FormField[1].Value),
            0
          ).toISOString(),
          zipCode: resPacket.message.Fields.FormField[6].Value,
          scheduledDepositDate: enrollment?.paymentInfo?.scheduledDepositDate,
        };
        if (!isPageEditing) {
          dispatch(setPaymentInfo(paymentInfo));
        }
        console.log('Security Payment Response:', JSON.stringify(req, null, 4));

        if (req.data.result.indicator === 'Success' && req.status === 200) {
          if (paymentType === '0') {
            router.push({
              pathname: props.fields.MiniConfirmationPageLink.value.href,
              query: { ...router.query },
            });
          }

          if (paymentType === '1') {
            scheduleRemainingDeposit(resPacket).then(
              () => {
                router.push({
                  pathname: props.fields.MiniConfirmationPageLink.value.href,
                  query: { ...router.query },
                });
              },
              () => {
                router.push({
                  pathname: '/oops',
                  query: {
                    ...router.query,
                  },
                });
              }
            );
          }
        } else {
          router.push({
            pathname: '/oops',
            query: {
              ...router.query,
              errorCode: req.data.result.indicator,
            },
          });
        }
      }
    } else {
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
          errorCode: connectCall && connectCall.result.indicator,
        },
      });
    }
  };

  const onSubmitDRSScriptEvent = () => {
    window.tsPlatform.drs
      .triggerActionEvent(props.fields.DRSActionName.value, {
        correlationId: `${correlationId}`,
      })
      .then((actionResponse: ActionResponse) => {
        console.log(actionResponse);
        console.log(`correlationId:${correlationId}`);
      })
      .catch((error: AxiosError) => {
        console.error('Error:', error);
      });
  };

  const orderAddonProducts = async () => {
    if (!isRenewableEnergy) return;
    try {
      const orderAddonReq = await axios.post<
        OrderAddonProductResponse,
        AxiosResponse<OrderAddonProductResponse>,
        OrderAddonProductRequest
      >(
        '/api/plans/addonplans',
        {
          businessPartnerNumber: enrollment?.enrollmentInfo?.bpNumber,
          contractAccountNumber: enrollment?.enrollmentInfo?.contractAccountNumber,
          esiid: enrollment?.serviceInfo?.esiid,
          zipCode: enrollment?.serviceInfo?.postalCode,
          currentPlanId: selectedPlan?.planId,
          dwellingType: DwellingType[dwel as string],
          language:
            enrollment?.customerInfo?.correspondanceLanguage === 'Español'
              ? 'Spanish'
              : enrollment?.customerInfo?.correspondanceLanguage,
          productOrders: [
            {
              productId: props.fields?.AddonPlanID?.value,
              quantity: 1,
            },
          ],
        },
        { headers: { 'Content-Type': 'application/json' } }
      );
      if (!isPageEditing) {
        dispatch(setOrderedPlans(orderAddonReq.data.result));
      }
    } catch (err) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'MiniConfirmationContainer-orderAddonProducts',
      });
      console.log(error);
    }
  };

  async function placeOrderDepositSubmit() {
    form.validate();
    if (form.isValid()) {
      if (props.fields.EnableDRS.value) onSubmitDRSScriptEvent();
      if (paymentType === '0' || paymentType === '1') {
        handleSubmit(getSecurityDeposit, accessToken);
      } else if (paymentType === '2' || paymentType === '3') {
        openLoaderModal();
        disableSubmit();

        const connectCall = await connect(
          enrollment,
          cint,
          dwel,
          prom,
          selectedPlan,
          getCNumber(),
          getANumber(anumber),
          web_experienceid,
          enrollment.autopayEligible,
          null,
          null,
          null,
          vendorid
        );

        if (connectCall && connectCall.result.indicator === 'Success') {
          if (!isPageEditing) {
            dispatch(setServiceAccountNumber(connectCall.result.serviceContractNumber));
          }
          const paymentInfo = {
            paymentMethod: '',
            priorDebtPaid: isPayPriorDebtNow,
            paymentType: paymentType,
            cardHolderName: '',
            cardNumber: '',
            expirationDate: '',
            zipCode: '',
            scheduledDepositDate: '',
          };
          if (!isPageEditing) {
            dispatch(setPaymentInfo(paymentInfo));
          }
          orderAddonProducts();

          router.push({
            pathname: props.fields.MiniConfirmationPageLink.value.href,
            query: { ...router.query },
          });
        } else {
          router.push({
            pathname: '/oops',
            query: {
              ...router.query,
              errorCode: connectCall && connectCall.result.indicator,
            },
          });
        }
      }
    }
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updateSchema = (newFields: Record<string, any>, newSchema: z.ZodObject<any>) => {
    setFormConfig((prevConfig) => ({
      initialValues: { ...prevConfig.initialValues, ...newFields },
      validationSchema: z.discriminatedUnion(
        prevConfig.validationSchema.discriminator,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        prevConfig.validationSchema.options.map((schema: any) => schema.merge(newSchema))
      ),
    }));
  };

  return (
    <div className="w-full flex flex-col sm:flex-row justify-center gap-[10px] sm:gap-5 4xl:gap-[60px] sm:mt-12 wide:flex-col ipad:flex-col wide:gap-[10px] ipad:gap-[10px]">
      <div className="">
        <form className="w-full">
          <Placeholder
            rendering={props.rendering}
            name="jss-placeorder-deposit"
            isPayPriorDebtNow={isPayPriorDebtNow}
            setIsPayPriorDebtNow={setIsPayPriorDebtNow}
            paymentType={paymentType}
            setPaymentType={setPaymentType}
            accessToken={accessToken}
            setAccessToken={setAccessToken}
            showReviewTermsTitle={solarAdded}
            updateSchema={updateSchema}
            onAutoPaysSelection={setAutopaySelected}
            form={form}
            render={(components) => {
              return (
                <div className="flex flex-col w-full mb-[60px] sm:mb-[80px] sm:items-center mt-4 sm:mt-0">
                  {components.map((component, index) => {
                    return (
                      <div
                        key={index}
                        className={`flex flex-col w-full items-center ${
                          index >= 2 ? 'sm:mt-[40px] mt-0' : ''
                        }`}
                      >
                        {component}
                      </div>
                    );
                  })}
                  <div className="w-full sm:w-[800px] flex justify-center sm:justify-start mt-8 px-[15px] sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
                    <Button
                      className="w-full h-[56px] md:w-[181px] rvPlaceOrderBtnn"
                      type="button"
                      onClick={placeOrderDepositSubmit}
                      //showLoader={form.isValid()}
                      disabled={submitDisabled}
                    >
                      {props.fields.PlaceOrderButtonLabel.value}
                    </Button>
                  </div>
                </div>
              );
            }}
          />
        </form>
      </div>
      <div className="w-full max-w-full sm:max-w-[400px] -mt-[4rem] sm:-mt-0">
        {props.rendering && (
          <Placeholder
            name="jss-rightside-section"
            rendering={props.rendering}
            displayWithContactDetails={true}
            render={(components) => (
              <div
                id="rightside-container"
                className="w-full flex flex-col items-center sm:items-end gap-[10px] wide:items-center wide:mt-[30px] ipad:items-center ipad:mt-[30px] px-6 sm:px-3 ipad:px-6 wide:px-6"
              >
                {components}
              </div>
            )}
          />
        )}
        <div className="w-full sm:w-[800px] flex sm:hidden justify-center sm:justify-start mt-4 mb-8 px-[15px] sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
          <Button
            className="w-full h-[56px] md:w-[181px] rvPlaceOrderBtnn"
            type="button"
            onClick={placeOrderDepositSubmit}
            //showLoader={form.isValid()}
            disabled={submitDisabled}
          >
            {props.fields.PlaceOrderButtonLabel.value}
          </Button>
        </div>
      </div>
    </div>
  );
};

export { PlaceOrderDepositContainer };
const Component = withDatasourceCheck()<PlaceOrderDepositContainerProps>(
  PlaceOrderDepositContainer
);
export default aiLogger(Component, Component.name);
