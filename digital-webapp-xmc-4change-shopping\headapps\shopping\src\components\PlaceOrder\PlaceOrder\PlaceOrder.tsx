import { Checkbox } from '@mantine/core';
import {
  Field,
  RichText,
  Text,
  useSitecoreContext,
  withDatasourceCheck,
} from '@sitecore-jss/sitecore-jss-nextjs';
import InfoText from 'components/common/InfoText/InfoText';
import PaymetricIntegration from 'components/PaymetricIntegration/PaymetricIntegration';
import dayjs from 'dayjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';

type PlaceOrderProps = ComponentProps & {
  fields: {
    TotalPaymentNow: Field<string>;
    PriorBalanceLabel: Field<string>;
    PaymentDetailsTitle: Field<string>;
    BalanceTitleText: Field<string>;
    CustomerTextBalance: Field<string>;
    AccountNumberLabel: Field<string>;
    FullNameLabel: Field<string>;
    ServiceAddressLabel: Field<string>;
    FinalBillDateLabel: Field<string>;
    AmountLabel: Field<string>;
    TotalBalanceLabel: Field<string>;
    DisputeChargesLabel: Field<string>;
    PayPriorBalanceNowLabel: Field<string>;
    PriorBalanceInfo: Field<string>;
    PlaceOrderTitleText: Field<string>;
    PlaceOrderDescriptionText: Field<string>;
  };
  isPayPriorDebtNow: boolean;
  accessToken: string;
  setAccessToken: React.Dispatch<React.SetStateAction<string>>;
  setIsPayPriorDebtNow: React.Dispatch<React.SetStateAction<boolean>>;
  setPaymentType: React.Dispatch<React.SetStateAction<string>>;
};

const PlaceOrder = (props: PlaceOrderProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let enrollment = undefined;
  if (!isPageEditing) enrollment = useAppSelector((state) => state.enrollment);
  const priorDebt = enrollment?.priorDebtInfo?.PriorDebt;
  const isPriorDebt = priorDebt?.length > 0;
  let totalPriorBalance = 0;
  if (isPriorDebt) {
    totalPriorBalance = priorDebt?.reduce((a, v) => (a = a + v.TotalDebt), 0);
  }

  return (
    <>
      <div className="place-order-section flex flex-col justify-start w-full px-5 md:w-[800px] md:px-0 gap-5 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
        <Text
          tag="p"
          className="font-primaryBlack   text-textPrimary text-xl md:text-plus3"
          field={props.fields.PlaceOrderTitleText}
        />
        <RichText
          tag="p"
          className="font-primaryRegular   text-textQuattuordenary md:text-lg text-minus1"
          field={props.fields.PlaceOrderDescriptionText}
        />
      </div>
      {isPriorDebt &&
      !enrollment?.priorDebtInfo?.ThresholdPassed &&
      enrollment?.depositAmount === 0 ? (
        <div className="justify-start flex flex-col gap-6 sm:gap-9 w-full px-5 sm:w-[800px] sm:px-0 md:mt-8">
          <div className="w-full flex flex-col gap-5">
            <Text
              tag="p"
              className="font-primaryBlack   text-textPrimary text-xl md:text-plus3"
              field={props.fields.BalanceTitleText}
            />
            <RichText
              tag="p"
              className="font-primaryRegular  text-minus1  text-textQuattuordenary md:text-lg"
              field={props.fields.CustomerTextBalance}
            />
          </div>
          <table className="w-full">
            <tbody>
              <tr className="text-left border-b-2 border-txublue h-11">
                <th className="text-txublue text-sm pr-2">
                  {props.fields.AccountNumberLabel.value}
                </th>
                <th className="text-txublue text-sm pr-2">{props.fields.FullNameLabel.value}</th>
                <th className="text-txublue text-sm pr-2">
                  {props.fields.ServiceAddressLabel.value}
                </th>
                <th className="text-txublue text-sm pr-2">
                  {props.fields.FinalBillDateLabel.value}
                </th>
                <th className="text-txublue text-sm pr-2">{props.fields.AmountLabel.value}</th>
              </tr>
              {priorDebt?.map((val, key) => {
                return (
                  <tr key={key} className="border-b-2 border-txublue h-11">
                    <td>{val.ContractAccountNumber}</td>
                    <td>{val.CustomerName}</td>
                    <td>{val.Address}</td>
                    <td>{dayjs(val.FinalBillDate).format('MM/DD/YYYY').toString()}</td>
                    <td>${val.TotalDebt.toFixed(2)}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="w-full flex flex-col gap-3 sm:gap-6">
            <Text
              tag="p"
              className="font-primaryRegular  text-minus1  text-textQuattuordenary md:text-lg"
              field={{
                value: `${props.fields.TotalBalanceLabel.value} : $${totalPriorBalance.toFixed(2)}`,
              }}
            />
            <Text
              tag="p"
              className="font-primaryRegular  text-minus1  text-textQuattuordenary md:text-lg"
              field={props.fields.DisputeChargesLabel}
            />
            <Checkbox
              label={
                <Text
                  tag="p"
                  className="font-primaryRegular   text-textQuattuordenary text-minus1 -translate-y-1"
                  field={props.fields.PayPriorBalanceNowLabel}
                />
              }
              onChange={(event) => props.setIsPayPriorDebtNow(event.currentTarget.checked)}
              checked={props.isPayPriorDebtNow}
            />
            {!props.isPayPriorDebtNow && (
              <Text
                tag="p"
                className="font-primaryRegular  text-minus1  text-textQuattuordenary md:text-lg"
                field={props.fields.PriorBalanceInfo}
              />
            )}
          </div>
        </div>
      ) : (
        <></>
      )}
      {props.isPayPriorDebtNow && enrollment?.depositAmount === 0 && (
        <>
          <div className="w-full sm:w-[800px] flex flex-col gap-4 sm:gap-7 md:mt-8">
            <Text
              tag="p"
              className="font-primaryBlack   text-textPrimary text-xl md:text-plus3"
              field={props.fields.PaymentDetailsTitle}
            />
          </div>

          <div className=" w-[864px] border-none bg-bgQuattuordenary rounded-none  px-5 pt-11 sm:px-8 sm:pt-14 sm:pb-8 gap-5">
            <div className="w-full flex flex-col  bg-transparent p-5">
              <div className="w-[343px] flex flex-col gap-2">
                <InfoText
                  label={props.fields.PriorBalanceLabel.value}
                  value={`$${totalPriorBalance.toFixed(2)}`}
                  fullWidth
                />
                <div className="w-full border-t-2 h-0 border-charcoal-50" />
                <InfoText
                  label={props.fields.TotalPaymentNow.value}
                  value={`$${totalPriorBalance.toFixed(2)}`}
                  fullWidth
                />
              </div>
            </div>
            <div>
              <PaymetricIntegration
                accessToken={props.accessToken}
                setAccessToken={props.setAccessToken}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export { PlaceOrder };
const Component = withDatasourceCheck()<PlaceOrderProps>(PlaceOrder);
export default aiLogger(Component, Component.name);
