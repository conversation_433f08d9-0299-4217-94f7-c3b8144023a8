import { Link<PERSON><PERSON>, Placeholder } from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import usePreventAbandon from 'src/hooks/usePreventAbandon';
//import useRouterEvents from 'src/hooks/useRouterEvents';

type PriorDebtPaymentConfirmationContainerProps = ComponentProps & {
  fields: {
    PlaceOrderPageLink: LinkField;
    DepositPaymentPageLink: LinkField;
  };
};

const PriorDebtPaymentConfirmationContainer = (
  props: PriorDebtPaymentConfirmationContainerProps
): JSX.Element => {
  const allowedUrls: (string | undefined)[] = [
    props.fields.PlaceOrderPageLink.value.href,
    props.fields.DepositPaymentPageLink.value.href,
    '/oops',
  ];
  usePreventAbandon(allowedUrls);
  return (
    <div className="w-full flex flex-col sm:flex-row justify-center gap-[10px] sm:gap-20 4xl:gap-[60px] sm:mt-12 wide:flex-col ipad:flex-col wide:gap-[10px] ipad:gap-[10px]">
      <div className="w-full max-w-[792px]">
        <Placeholder
          name="jss-placeorder-confirmation"
          rendering={props.rendering}
          render={(components) => {
            return (
              <div className="flex flex-col w-full mb-[60px] sm:mb-[80px] sm:items-center">
                {components.map((component, index) => {
                  return (
                    <div
                      key={index}
                      className={`flex flex-col w-full items-center ${
                        index >= 2 ? 'mt-[32px] sm:mt-[40px] wide:px-[20px] ipad:px-[40px]' : ''
                      }`}
                    >
                      {component}
                    </div>
                  );
                })}
              </div>
            );
          }}
        />
      </div>
      <div className="w-full max-w-full sm:max-w-[400px] -mt-[4rem]  sm:-mt-0">
        {props.rendering && (
          <Placeholder
            name="jss-rightside-section"
            rendering={props.rendering}
            displayWithContactDetails={true}
            render={(components) => (
              <div
                id="rightside-container"
                className="w-full flex flex-col items-center sm:items-end gap-[10px] wide:items-center wide:mt-[30px] ipad:items-center ipad:mt-[30px] px-6 sm:px-3 ipad:px-6 wide:px-6"
              >
                {components}
              </div>
            )}
          />
        )}
      </div>
    </div>
  );
};

export { PriorDebtPaymentConfirmationContainer };
export default aiLogger(
  PriorDebtPaymentConfirmationContainer,
  PriorDebtPaymentConfirmationContainer.name
);
