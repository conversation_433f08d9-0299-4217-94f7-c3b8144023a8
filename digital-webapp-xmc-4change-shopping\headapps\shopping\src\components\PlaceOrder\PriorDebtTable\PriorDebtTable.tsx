import aiLogger from 'src/hoc/ApplicationInsightsLogger';

type PriorDebtTableProps<T, K> = {
  keys: K[];
  data: T[];
  header: string[];
};

const PriorDebtTable = <T extends Record<string, string | number>, K extends keyof T>(
  props: PriorDebtTableProps<T, K>
): JSX.Element => {
  console.log(props);
  return (
    <>
      <div className="block sm:hidden mt-5">
        {props.data &&
          props.data.map((val, number) => {
            return (
              <table
                className={`w-full border-t-2 border-txublue ${
                  number === props.data.length - 1 ? 'border-b-2' : ''
                }`}
                key={number}
              >
                <tbody className="block py-5">
                  {props.keys.map((value, index) => {
                    return (
                      <tr
                        key={index}
                        className="text-left border-txublue pb-[8px] grid grid-cols-2"
                      >
                        <th className=" text-textPrimary text-sm">{props.header[index]}</th>
                        <td className="text-sm">{val[value]}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            );
          })}
      </div>
      <div className="hidden sm:block">
        <table className="w-full">
          <tbody>
            <tr className="text-left border-b-2 border-txublue h-11">
              {props.header.map((label, number) => {
                return (
                  <th key={number} className=" text-textPrimary text-sm pr-2">
                    {label}
                  </th>
                );
              })}
            </tr>
            {props.data.map((val, key) => {
              return (
                <tr key={key} className="border-b-2 border-txublue h-11">
                  {props.keys.map((value, number) => {
                    return (
                      <td key={number} className="text-sm">
                        {val[value]}
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default aiLogger(PriorDebtTable, PriorDebtTable.name);
