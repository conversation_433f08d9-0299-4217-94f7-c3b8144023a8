import { useDisclosure } from '@mantine/hooks';
import {
  Field,
  LinkField,
  Placeholder,
  useSitecoreContext,
  withDatasourceCheck,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios, { AxiosError, AxiosResponse } from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { PaymetricResponse } from 'components/PaymetricIntegration/PaymetricIntegration';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import usePreventAbandon from 'src/hooks/usePreventAbandon';
//import useRouterEvents from 'src/hooks/useRouterEvents';
import {
  PriorDebtCardPaymentResponse,
  PriorDebtData,
  PriorDebtPaymentBody,
} from 'src/services/EnrollmentAPI/types';
import { setPriorDebtSuccessPaymentInfo } from 'src/stores/enrollmentSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import handleSubmit from 'src/utils/placeOrderSubmit';

type PriorDebtContainerProps = ComponentProps & {
  fields: {
    PriorDebtConfirmationLink: LinkField;
    ContinueButtonLabel: Field<string>;
  };
};

const PriorDebtContainer = (props: PriorDebtContainerProps): JSX.Element => {
  const allowedUrls: (string | undefined)[] = [
    props.fields.PriorDebtConfirmationLink.value.href,
    '/oops',
  ];
  usePreventAbandon(allowedUrls);
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  const [accessToken, setAccessToken] = useState<string>('');
  const [isPaymentFailed, setIsPaymentFailed] = useState<boolean>(false);
  const [paymentFailureMessage, setPaymentFailureMessage] = useState<string>('');
  const [submitDisabled, { open: disableSubmit, close: enableSubmit }] = useDisclosure(false);
  let enrollment = undefined;
  let priorDebtSuccess = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;
  const [btnLoading, setBtnLoading] = useState(true);
  if (!isPageEditing) {
    dispatch = useAppDispatch();
    enrollment = useAppSelector((state) => state?.enrollment);
    priorDebtSuccess = useAppSelector((state) => state?.enrollment?.PriorDebtSuccessPaymentInfo);
  }
  let priorDebtSuccessKeys: string[] = [];
  if (priorDebtSuccess !== undefined) {
    priorDebtSuccessKeys = Object.keys(priorDebtSuccess);
  }

  const router = useRouter();
  const remainingPriorDebtData: PriorDebtData[] = [];

  enrollment?.priorDebtInfo?.PriorDebt?.forEach((priorDebt) => {
    if (!priorDebtSuccessKeys.includes(priorDebt.ServiceContractNumber)) {
      remainingPriorDebtData.push(priorDebt);
    }
  });

  const makePriordebtPaymentCard = async (resPacket: PaymetricResponse): Promise<void> => {
    setBtnLoading(true);
    disableSubmit();
    let isPriorDebtPartialSuccess = false;
    let cardType = resPacket.message.Fields.FormField[0].Value;
    if (cardType === 'vi') {
      cardType = 'Visa';
    } else if (cardType === 'mc') {
      cardType = 'Mastercard';
    } else {
      cardType = 'Credit';
    }

    try {
      const req = await axios.post<
        PriorDebtCardPaymentResponse,
        AxiosResponse<PriorDebtCardPaymentResponse, PriorDebtPaymentBody>,
        PriorDebtPaymentBody
      >('/api/payments/paymetric/priordebtpaymentcard', {
        PaymentDate: new Date().toISOString(),
        expiration: new Date(
          parseInt(resPacket.message.Fields.FormField[2].Value),
          parseInt(resPacket.message.Fields.FormField[1].Value),
          0
        ).toISOString(),
        cardType: 'Credit',
        HolderName: resPacket.message.Fields.FormField[4].Value,
        contractAccountNumber: enrollment?.enrollmentInfo?.contractAccountNumber,
        nickname: resPacket.message.Fields.FormField[4].Value,
        accountId: resPacket.message.Fields.FormField[3].Value,
        profileId: resPacket.message.Fields.FormField[3].Value.substring(12, 16),
        paymentType: 0,
        payChannel: 'Web',
        payAgent: '',
        tenderType: cardType,
        PaySource: '',
        bpNumber: enrollment?.enrollmentInfo?.bpNumber ? enrollment?.enrollmentInfo.bpNumber : '',
        PriorDebtCollection: remainingPriorDebtData,
        BillingPostalCode: resPacket.message.Fields.FormField[6].Value,
        Cvv: resPacket.message.Fields.FormField[5].Value,
      });
      const priorDebtResponse = req.data;

      console.log('PriorDebtResponse', priorDebtResponse);

      if (priorDebtResponse.hasErrors) {
        router.push({
          pathname: '/oops',
          query: {
            ...router.query,
          },
        });
      }

      const paymentResultCollection = priorDebtResponse.result.paymentResultCollection;

      if (paymentResultCollection.length !== 0) {
        const successPayments = paymentResultCollection.filter(
          (value) => value.paymentResult.indicator === 'Success'
        );

        const failurepayments = paymentResultCollection.filter(
          (value) => value.paymentResult.indicator !== 'Success'
        );

        console.log('Success payments', successPayments);
        if (!isPageEditing) {
          dispatch(setPriorDebtSuccessPaymentInfo(successPayments));
        }
        let paymentCollectionContractNumbers = successPayments.map(
          (paymentResult) => paymentResult.priorDebtDetail.ContractAccountNumber
        );

        paymentCollectionContractNumbers =
          paymentCollectionContractNumbers.concat(priorDebtSuccessKeys);

        for (let i = 0; i < enrollment?.priorDebtInfo?.PriorDebt?.length; i++) {
          if (
            !paymentCollectionContractNumbers.includes(
              enrollment?.priorDebtInfo.PriorDebt[i]?.ContractAccountNumber
            )
          ) {
            isPriorDebtPartialSuccess = true;
            for (let j = 0; j < failurepayments.length; j++) {
              setIsPaymentFailed(true);
              setPaymentFailureMessage(failurepayments[j].paymentResult.message);
            }
            setAccessToken('');
            window.scrollTo(0, 0);
            setBtnLoading(false);
          }
        }

        if (!isPriorDebtPartialSuccess) {
          router.push({
            pathname: props.fields.PriorDebtConfirmationLink.value.href,
            query: { ...router.query },
          });
        } else {
          enableSubmit();
        }
      }
    } catch (error) {
      const err = error as AxiosError;

      logErrorToAppInsights(err, {
        componentStack: 'PriorDebtContainer',
      });
      router.push({
        pathname: '/oops',
        query: {
          ...router.query,
        },
      });
      return;
    }
  };

  async function placeOrderPriorDebtSubmit() {
    handleSubmit(makePriordebtPaymentCard, accessToken);
  }

  return (
    <div className="w-full flex flex-col sm:flex-row justify-center gap-[10px] sm:gap-20 4xl:gap-[60px] sm:mt-12 wide:flex-col ipad:flex-col wide:gap-[10px] ipad:gap-[10px]">
      <div>
        <form className="w-full max-w-[792px]">
          <Placeholder
            rendering={props.rendering}
            name="jss-placeorder-priordebt"
            accessToken={accessToken}
            setAccessToken={setAccessToken}
            isPaymentFailed={isPaymentFailed}
            setIsPaymentFailed={setIsPaymentFailed}
            paymentFailureMessage={paymentFailureMessage}
            setPaymentFailureMessage={setPaymentFailureMessage}
            render={(components) => {
              return (
                <div className="flex flex-col w-full mb-[60px] sm:mb-[80px] sm:items-center">
                  {components.map((component, index) => {
                    return (
                      <div
                        key={index}
                        className={`flex flex-col w-full items-center ${
                          index >= 2 ? 'mt-[32px] sm:mt-[40px] wide:px-[20px] ipad:px-[20px]' : ''
                        }`}
                      >
                        {component}
                      </div>
                    );
                  })}
                  <div className="w-full sm:w-[800px] flex justify-center sm:justify-start mt-8 px-[15px] sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
                    <Button
                      className="h-[56px] w-auto md:w-[190px]"
                      type="button"
                      onClick={placeOrderPriorDebtSubmit}
                      disabled={submitDisabled}
                      showLoader={btnLoading}
                    >
                      {props.fields.ContinueButtonLabel.value}
                    </Button>
                  </div>
                </div>
              );
            }}
          />
        </form>
      </div>

      <div className="w-full max-w-full sm:max-w-[400px] -mt-[4rem]  sm:-mt-0">
        {props.rendering && (
          <Placeholder
            name="jss-rightside-section"
            rendering={props.rendering}
            displayWithContactDetails={true}
            render={(components) => (
              <div
                id="rightside-container"
                className="w-full flex flex-col items-center sm:items-end gap-[10px] wide:items-center wide:mt-[30px] ipad:items-center ipad:mt-[30px] px-6 sm:px-3 ipad:px-6 wide:px-6"
              >
                {components}
              </div>
            )}
          />
        )}
      </div>
    </div>
  );
};

export { PriorDebtContainer };
const Component = withDatasourceCheck()<PriorDebtContainerProps>(PriorDebtContainer);
export default aiLogger(Component, Component.name);
