import {
  Field,
  withDatasourceCheck,
  Text,
  Placeholder,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import PrintSelectedPlanCard from 'components/common/PrintSelectedPlanCard/PrintSelectedPlanCard';
import { useRef } from 'react';
import { useAppSelector } from 'src/stores/store';
import PrintPageButton from 'components/Elements/PrintPageButton/PrintPageButton';

type WelcomeBlockWithPlanDetailsProps = ComponentProps & {
  fields: {
    AccountNumberLabel: Field<string>;
    ConfirmationNumberLabel: Field<string>;
    Title: Field<string>;
    RequestServiceMessage: Field<string>;
    PlanSummary: Field<string>;
    PrintText: Field<string>;
    AddressLabel: Field<string>;
    StartDateLabel: Field<string>;
    BillingAddressLabel: Field<string>;
    WhatsNextLabel: Field<string>;
    POBoxLabel: Field<string>;
  };
};

const WelcomeBlockWithPlanDetails = (props: WelcomeBlockWithPlanDetailsProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let enrollmentInfo = undefined;
  let customerInfo = undefined;
  let startDate = undefined;
  let enrollment = undefined;
  if (!isPageEditing) {
    enrollmentInfo = useAppSelector((state) => state.enrollment?.enrollmentInfo);
    customerInfo = useAppSelector((state) => state.enrollment?.customerInfo);
    startDate = useAppSelector((state) => state.enrollment?.startDate);
    enrollment = useAppSelector((state) => state.enrollment);
  }
  const printRef = useRef<HTMLDivElement>(null);
  const serviceInfo = enrollment?.serviceInfo;
  const billingInfo = enrollment?.billingInfo;
  const currentDate = new Date();

  return (
    <div>
      <div
        className="mini-confirmation w-full max-w-[924px] sm:px-[15px] px-[15px] flex flex-col gap-5 print:w-11/12 print:m-5 mt-[40px] mb-0 sm:mb-[20px] sm:my-[40px]"
        ref={printRef}
      >
        <div className="flex flex-col mt-[4px]  justify-center">
          <Text
            tag="span"
            className="flex justify-center leading-[30px] text-plus2 font-primaryBold md:text-2xl text-textQuattuordenary text-center sm:text-left"
            field={{
              value: props.fields?.Title?.value.replace(
                '{firstName}',
                customerInfo?.firstName?.trim()
              ),
            }}
          />
          <Text
            tag="p"
            className="flex justify-center leading-[30px] font-primaryBold md:text-3xl text-textQuattuordenary text-center sm:text-left"
            field={props?.fields?.RequestServiceMessage}
          />
          <Text
            tag="p"
            className="flex justify-center leading-[30px] text-plus2 font-primaryBold md:text-2xl pt-[20px] text-textQuattuordenary text-center sm:text-left"
            field={{
              value: props.fields?.ConfirmationNumberLabel?.value.replace(
                '{ConfirmationId}',
                enrollmentInfo?.serviceAccountNumber?.trim()
              ),
            }}
          />
          <p
            className="flex justify-center leading-[30px] text-plus2 font-primaryRegular md:text-xl mt-[12px]
          text-textQuattuordenary text-center sm:text-left"
          >
            {currentDate?.toLocaleString()}
          </p>
        </div>
        <div className="text-center mt-0">
          <PrintPageButton
            label={props.fields.PrintText.value}
            printRef={printRef}
            pageName="mini-confirmation"
          />
        </div>
        <div className="bg-bgQuinary border-borderQuaternary border-[2px] rounded-[20px] p-3 sm:p-8 mt-10">
          <div className="justify-between flex flex-col sm:flex-row sm:gap-0 gap-5">
            <div className="flex flex-col sm:w-[33.3%] w-full sm:pr-3">
              <Text
                tag="p"
                className="text-textQuattuordenary font-primaryBold text-[24px]"
                field={props.fields.AccountNumberLabel}
              />
              <Text
                tag="p"
                className="font-primaryRegular pt-2 text-textQuattuordenary text-[16px] leading-[24px] pl-1 break-word"
                field={{ value: enrollmentInfo?.contractAccountNumber?.trim() }}
              />
            </div>
            <div className="flex flex-col sm:w-[33.3%] w-full sm:pr-3">
              <Text
                tag="p"
                className="font-primaryBold text-textQuattuordenary text-[24px]"
                field={props.fields.AddressLabel}
              />
              <Text
                tag="p"
                className="font-primaryRegular  pt-4 text-textQuattuordenary text-[16px] leading-[24px] pl-1 break-word w-[60%]"
                field={{
                  value: `${serviceInfo?.houseNbr ?? ''} ${serviceInfo?.street ?? ''} ${
                    serviceInfo?.unit ?? ''
                  } ${serviceInfo?.city ?? ''}, ${serviceInfo?.state ?? ''} ${
                    serviceInfo?.postalCode ?? ''
                  }`.trim(),
                }}
              />
              <Text
                tag="p"
                className="font-primaryRegular pt-4 text-textQuattuordenary text-[16px]  pl-1 break-word"
                field={props.fields.StartDateLabel}
              />
              <Text
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-[16px] pl-1 break-word"
                field={{ value: startDate }}
              />
            </div>
            <div className="flex flex-col sm:w-[33.3%] w-full">
              <Text
                tag="p"
                className="font-primaryBold text-textQuattuordenary text-[24px]"
                field={props.fields.BillingAddressLabel}
              />
              <Text
                tag="p"
                className="font-primaryRegular  pt-4 text-textQuattuordenary text-[16px] leading-[24px] pl-1 break-word w-[60%]"
                field={{
                  value: !billingInfo?.isSameAddress
                    ? serviceInfo?.poBoxZipCode && serviceInfo?.poBoxZipCode.trim() !== ''
                      ? `${props?.fields?.POBoxLabel?.value ?? ''} ${serviceInfo?.poBox ?? ''} ${
                          serviceInfo?.poBoxCity ?? ''
                        }, ${serviceInfo?.poBoxState ?? ''} ${
                          serviceInfo?.poBoxZipCode ?? ''
                        }`.trim()
                      : `${billingInfo?.billingStreetNumber ?? ''} ${
                          billingInfo?.billingStreetAddress ?? ''
                        } ${billingInfo?.billingAptOrUnit ?? ''} ${
                          billingInfo?.billingCity ?? ''
                        }, ${billingInfo?.billingState ?? ''} ${
                          billingInfo?.billingZipCode ?? ''
                        }`.trim()
                    : `${serviceInfo?.houseNbr ?? ''} ${serviceInfo?.street ?? ''} ${
                        serviceInfo?.unit ?? ''
                      } ${serviceInfo?.city ?? ''}, ${serviceInfo?.state ?? ''} ${
                        serviceInfo?.postalCode ?? ''
                      }`.trim(),
                }}
              />
            </div>
          </div>
          <div className="mt-[40px]">
            <Placeholder
              name="jss-welcomeblock-details"
              rendering={props.rendering}
              render={(components) => <div id="welcomeblock-container">{components}</div>}
            />
            <Text
              tag="p"
              className="font-primaryBold text-[32px] text-plus2 sm:text-plus3 text-textQuattuordenary leading-[38px] mt-[40px]"
              field={props.fields.WhatsNextLabel}
            />
            <Text
              tag="p"
              className="font-primaryRegular text-textQuattuordenary text-minus1 text-[18px] leading-[28px] mt-[14px]"
              field={props.fields.PlanSummary}
            />
          </div>
        </div>
        <div className="hidden print:block">
          <PrintSelectedPlanCard
            fields={{
              PlanSummary: props.fields.PlanSummary,
              WhatsNextLabel: props.fields.WhatsNextLabel,
            }}
            rendering={props.rendering}
            params={props.params}
          />
        </div>
      </div>
    </div>
  );
};

export { WelcomeBlockWithPlanDetails };
const Component = withDatasourceCheck()<WelcomeBlockWithPlanDetailsProps>(
  WelcomeBlockWithPlanDetails
);
export default aiLogger(Component, Component.name);
