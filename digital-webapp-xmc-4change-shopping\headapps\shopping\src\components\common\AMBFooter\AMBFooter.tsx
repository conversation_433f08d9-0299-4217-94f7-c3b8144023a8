import {
  Field,
  withDatasource<PERSON>heck,
  Image,
  ImageField,
  LinkField,
  RichText,
  Link,
} from '@sitecore-jss/sitecore-jss-nextjs';
import ExternalLink from 'components/Elements/ExternalLink/ExternalLink';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';

type AMBFooterProps = ComponentProps & {
  fields: {
    AmbitLogo: ImageField;
    AmbitLogoLink: LinkField;
    BBBLogo: ImageField;
    BBBLogoLink: LinkField;
    PrivacyPolicyText: Field<string>;
    TermsandconditionsText: Field<string>;
    DSACodeofEthicsText: Field<string>;
    CopyrightText: Field<string>;
    PrivacyPolicyLink: LinkField;
    TermsandconditionsLink: LinkField;
    DSACodeofEthicsLink: LinkField;
    LicensedInText: Field<string>;
    StateRatesText: Field<string>;
    StateRatesLink: LinkField;
  };
};

const AMBFooter = (props: AMBFooterProps): JSX.Element => {
  return (
    <footer className="bg-bgFooter p-5 sm:p-0">
      {/* footer */}
      <div className="flex flex-col w-full items-center bg-bgFooter pb-10 px-0 sm:px-6">
        {/* row 1 */}
        <div className="flex flex-col sm:flex-row w-full max-w-[1100px] items-center text-left mt-[70px] sm:gap-10 gap-0">
          <div className="flex w-full lg:w-2/12 justify-start">
            {props?.fields?.AmbitLogoLink?.value && props?.fields?.AmbitLogo && (
              <ExternalLink
                title="Logo"
                href={props?.fields?.AmbitLogoLink?.value.href as string}
                className="w-auto h-[45px] sm:h-[65px] mt-3 sm:mt-0"
              >
                <Image className="w-auto h-[45px] md:ml-0" field={props?.fields?.AmbitLogo} />
              </ExternalLink>
            )}
          </div>

          <div>
            <div className="w-full flex mt-4 sm:mt-0">
              <ul className="text-[20px] text-textFooter font-primaryBold tracking-normal flex flex-col sm:flex-row w-full items-center sm:gap-10">
                {props?.fields?.PrivacyPolicyLink?.value &&
                  props?.fields?.PrivacyPolicyText?.value && (
                    <li className="w-full sm:w-auto p-3 pl-0">
                      <Link field={props?.fields?.PrivacyPolicyLink?.value}>
                        {props?.fields?.PrivacyPolicyText?.value}
                      </Link>
                    </li>
                  )}
                {props?.fields?.TermsandconditionsLink?.value &&
                  props?.fields?.TermsandconditionsText?.value && (
                    <li className="w-full sm:w-auto p-3 pl-0">
                      <Link field={props?.fields?.TermsandconditionsLink?.value}>
                        {props?.fields?.TermsandconditionsText?.value}
                      </Link>
                    </li>
                  )}
                {props?.fields?.DSACodeofEthicsLink?.value &&
                  props?.fields?.DSACodeofEthicsText?.value && (
                    <li className="w-full sm:w-auto p-3 pl-0 sm:pl-4">
                      <Link field={props?.fields?.DSACodeofEthicsLink?.value}>
                        {props?.fields?.DSACodeofEthicsText?.value}
                      </Link>
                    </li>
                  )}
              </ul>
            </div>
            <div className="flex flex-col w-full items-center mt-3 sm:mt-0 ">
              <div className="flex flex-col w-full max-w-[1100px] text-left">
                {props?.fields?.CopyrightText && (
                  <RichText
                    className="font-primaryRegular text-minus1 text-textFooter w-full"
                    field={props?.fields?.CopyrightText}
                    tag="p"
                  />
                )}
                {props?.fields?.LicensedInText && (
                  <RichText
                    className="font-primaryRegular text-minus1 text-textFooter w-full mt-5 sm:mt-1"
                    field={props?.fields?.LicensedInText}
                    tag="p"
                  />
                )}
                {props?.fields?.StateRatesText && (
                  <RichText
                    className="font-primaryRegular text-minus1 text-textFooter w-full mt-5 sm:mt-1"
                    field={props?.fields?.StateRatesText}
                    tag="p"
                  />
                )}
              </div>
            </div>
          </div>

          <div className="w-full lg:w-2/12 mt-4 sm:mt-0 sm:flex">
            {props?.fields?.BBBLogo && (
              <Link
                field={props?.fields?.BBBLogoLink?.value}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  alt="BBB Logo"
                  className="mt-10 mb-10 w-[120px] sm:max-w-[100px]"
                  field={props?.fields?.BBBLogo}
                />
              </Link>
            )}
          </div>
        </div>

        {/* row 2 */}
      </div>
      {/* end of footer */}
    </footer>
  );
};

export { AMBFooter };
const Component = withDatasourceCheck()<AMBFooterProps>(AMBFooter);
export default aiLogger(Component, Component.name);
