import { Loader, Popover, TextInput } from '@mantine/core';
import { DatePicker, DateValue } from '@mantine/dates';
import { UseFormReturnType } from '@mantine/form';
import { useClickOutside, useDisclosure } from '@mantine/hooks';
import { RichText } from '@sitecore-jss/sitecore-jss-nextjs';
import dayjs from 'dayjs';
import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight, faCalendarMinus, faChevronLeft } from '@fortawesome/pro-light-svg-icons';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons';
import { GetConnectDate } from 'src/services/CalendarAPI/types';
import CalendarValidator from 'src/utils/calendarValidator';
import { formatString } from 'src/utils/formatString';
import { PopoverMiddlewares } from '@mantine/core/lib/Popover/Popover.types';
import CalendarCheck from 'assets/icons/Calendar';

interface CalendarProps<T> {
  form: UseFormReturnType<T>;
  formField: keyof T;
  calendarData: GetConnectDate;
  calendarDesclaimer?: string;
  calendarPriorityDisclaimer?: string;
  calendarDays: number;
  calendarValidator: CalendarValidator;
  error: React.ReactNode;
  popoverMiddlewares?: PopoverMiddlewares;
}

function Calendar<T>({
  form,
  formField,
  calendarData,
  calendarDays,
  calendarDesclaimer,
  calendarPriorityDisclaimer,
  calendarValidator,
  error,
  popoverMiddlewares,
}: CalendarProps<T>): JSX.Element {
  const [calendarInputRef, setCalendarInputRef] = useState<HTMLInputElement | null>(null);
  const [calendarPopupOpened, { open: openCalendar, close: closeCalendar }] = useDisclosure(false);
  const [calendarRef, setCalendarRef] = useState<HTMLDivElement | null>(null);
  const [calendarIconRef, setCalendarIconRef] = useState<HTMLDivElement | null>(null);
  const [selectStartDate, setSelectStartDate] = useState('');
  console.log(error);

  useClickOutside(
    () => {
      if (calendarPopupOpened) closeCalendar();
    },
    null,
    [calendarRef, calendarInputRef, calendarIconRef]
  );

  const onSelectStartDate = (value: DateValue) => {
    const date = dayjs(value).format('MM/DD/YYYY').toString();
    form.setFieldValue(formField, Object(date).toString());
    setSelectStartDate(date);
    closeCalendar();
  };

  return (
    <div className="flex flex-col gap-3">
      <Popover
        opened={calendarPopupOpened}
        width={315}
        position="bottom-start"
        trapFocus
        middlewares={popoverMiddlewares}
        styles={{ dropdown: { border: '1px solid #297F9D' } }}
      >
        <Popover.Target>
          <TextInput
            {...form.getInputProps(formField)}
            readOnly
            onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
              if (event.code === 'Enter') openCalendar();
              if (event.code === 'Escape') closeCalendar();
            }}
            ref={setCalendarInputRef}
            onClick={calendarPopupOpened ? closeCalendar : openCalendar}
            styles={{
              root: { width: '280px' },
              input: {
                paddingRight: '50px',
              },
            }}
            disabled={!calendarData}
            rightSection={
              calendarData ? (
                <div
                  onClick={calendarPopupOpened ? closeCalendar : openCalendar}
                  onKeyDown={(event: React.KeyboardEvent<HTMLDivElement>) => {
                    console.log(event.code);
                  }}
                  ref={setCalendarIconRef}
                  className="flex flex-row w-full"
                >
                  <FontAwesomeIcon
                    icon={faCircleCheck}
                    className={`text-textSexdenary text-[24px] ml-4 ${selectStartDate ? ' visible' : 'invisible'
                      }`}
                  />
                  <div className="text-textVigintiduonary hover:text-textSecondary cursor-pointer ml-auto mr-4 text-[24px]">
                    <CalendarCheck />
                  </div>
                </div>
              ) : (
                <Loader size="sm" />
              )
            }
            rightSectionWidth={calendarData ? 180 : 50}
          />
        </Popover.Target>
        <Popover.Dropdown>
          <div ref={setCalendarRef}>
            <DatePicker
              styles={{
                calendarHeader: {
                  minWidth: '280px',
                },
              }}
              minDate={
                calendarData
                  ? dayjs(
                    calendarData?.result.priorityDays.length > 0
                      ? calendarData?.result.priorityDays[0]
                      : calendarData?.result.standardDays.length > 0
                        ? calendarData?.result.standardDays[0]
                        : new Date()
                  ).toDate()
                  : undefined
              }
              maxDate={
                calendarData
                  ? dayjs(
                    calendarData?.result.priorityDays.length > 0
                      ? calendarData?.result.priorityDays[0]
                      : calendarData?.result.standardDays.length > 0
                        ? calendarData?.result.standardDays[0]
                        : new Date()
                  )
                    .add(calendarDays, 'day')
                    .toDate()
                  : undefined
              }
              excludeDate={(date) =>
                calendarValidator ? !calendarValidator.isDateValid(date) : false
              }
              defaultDate={
                calendarData
                  ? dayjs(
                    calendarData?.result.priorityDays.length > 0
                      ? calendarData?.result.priorityDays[0]
                      : calendarData?.result.standardDays.length > 0
                        ? calendarData?.result.standardDays[0]
                        : new Date()
                  ).toDate()
                  : undefined
              }
              defaultValue={dayjs(calendarData?.result.standardDays[0]).toDate()}
              weekendDays={[]}
              firstDayOfWeek={0}
              hideOutsideDates
              previousIcon={
                <FontAwesomeIcon
                  icon={faChevronLeft}
                  className="text-textPrimary hover:text-textSecondary "
                />
              }
              nextIcon={
                <FontAwesomeIcon
                  icon={faChevronRight}
                  className="text-textPrimary hover:text-textSecondary "
                />
              }
              onChange={onSelectStartDate}
              value={dayjs(selectStartDate).toDate()}
              getDayProps={(date) => {
                const isPriorityDay = calendarValidator?.isPriorityDay(date);
                return {
                  sx: (theme) => ({
                    color: isPriorityDay
                      ? theme.other.colors.textDenary
                      : theme.other.colors.textQuattuordenary,
                    width: '35px',
                    height: '27px',
                    borderRadius: '0px',
                    border: isPriorityDay
                      ? `0px solid ${theme.other.colors.borderPrimary}`
                      : `0px solid ${theme.other.colors.borderSexdenary}`,
                    margin: '2px',
                  }),
                };
              }}
            />

            {calendarDesclaimer && (
              <RichText
                field={{
                  value: formatString(calendarDesclaimer, [
                    '$' + calendarData.result.standardFee.toFixed(2),
                    '$' + calendarData.result.priorityFee.toFixed(2),
                  ]),
                }}
              />
            )}

            {calendarPriorityDisclaimer &&
              calendarData.result.priorityFee.toFixed(2) !== '0.00' && (
                <RichText
                  field={{
                    value: formatString(calendarPriorityDisclaimer, [
                      '$' + calendarData.result.priorityFee.toFixed(2),
                    ]),
                  }}
                />
              )}
          </div>
        </Popover.Dropdown>
      </Popover>
    </div>
  );
}

export default Calendar;
