import { faGift } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Field,
  Text,
  withDatasourceCheck,
  Link,
  RichText,
  GetServerSideComponentProps,
  useComponentProps,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useEffect, useState } from 'react';
import {
  faChevronDown as faChevronDownSolid,
  faChevronUp as faChevronUpSolid,
} from '@fortawesome/pro-solid-svg-icons';
import DownloadIcon from 'assets/icons/DownloadIcon';
import { useAppSelector } from 'src/stores/store';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';
import { useRouter } from 'next/router';
import { cn } from 'src/utils/cn';
import { isAMB } from 'src/utils/util';
import { useI18n } from 'next-localization';
import axios from 'axios-1.4';
import { v4 } from 'uuid';
import { Offers } from 'src/services/ViewOffersAPI/types';
import { Loader } from '@mantine/core';
import { Incetives } from 'components/ViewPlans/AMBPlanCardList/AMBPlanCardList';
import { getPlanIncentives } from 'src/utils/getIncentives';

interface getOfferParams {
  sessionid?: string;
  access_token: string;
  locale: string;
}

interface SelectedPlanDetails {
  oneLineSummary: string;
  planBenefits: string;
  planDisclaimer: string;
  incentiveId: string;
  incentiveDisclaimer: string;
  incentiveSpecialOfferText: string;
}

type SelectedPlanCardWithDetailsProps = ComponentProps & {
  fields: {
    Heading: Field<string>;
    PerkWhText: Field<string>;
    PerkWhTextApt: Field<string>;
    TermLabelText: Field<string>;
    RateTypeLabelText: Field<string>;
    MobileNumber: Field<string>;
    ShowDetailsText: Field<string>;
    HideDetailsText: Field<string>;
    PriceByUsageText: Field<string>;
    MonthlyUsageText: Field<string>;
    NeedHelpText: Field<string>;
    NeedHelpPhNumber: Field<string>;
    AvgPricePerkWhText: Field<string>;
    AvgDigitalPricePerkWhText: Field<string>;
    AveragePriceDescription: Field<string>;
    EnergyRateText: Field<string>;
    BaseChargeText: Field<string>;
    EarlyCancellationFeeText: Field<string>;
    kWhText: Field<string>;
    ElectricityFactsLabelText: Field<string>;
    TermsOfServiceText: Field<string>;
    YourRightsAsaCustomerText: Field<string>;
    ECFPricingText: Field<string>;
    EnergyChargeValue: Field<string>;
    BaseChargeValue: Field<string>;
    MonthsLabelText: Field<string>;
    DigitalRateLabelText: Field<string>;
    IncentivesList: Incetives[];
  };
};

const SelectedPlanCardWithDetails = (props: SelectedPlanCardWithDetailsProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let selectedPlan = undefined;
  if (!isPageEditing) {
    selectedPlan = useAppSelector((state) => state.plans?.selectedPlan);
  }
  console.log(props);
  const { locale } = useI18n();
  const [showDetails, setShowDetails] = useState(true);
  const router = useRouter();
  const { cint, prom, dwel, zip, tdsp, vendorid } = router.query as QueryParamsMapType;
  const data = useComponentProps<getOfferParams>(props.rendering?.uid);
  const access_token = data?.access_token;
  const sessionId = data?.sessionid;
  const [selectedPlanDetails, setSelectedPlanDetails] = useState<SelectedPlanDetails | undefined>(
    undefined
  );
  const energyCharge = selectedPlan?.trieProductRateDetails?.find(
    (item) =>
      item.conditionType === 'Z100' ||
      item.conditionType === 'ZESC' ||
      item.conditionType === 'Z250'
  );
  const renderdigitalRate = (val: string | number) => {
    const digitalRecord = selectedPlan?.ePlanRates?.find(
      (item) => item?.description?.toString() === val?.toString()
    );
    return digitalRecord?.value != undefined ? (digitalRecord?.value * 100)?.toFixed(1) : '';
  };

  useEffect(() => {
    const fetchData = async () => {
      const plans = await axios.get('/api/offers/fetchplans', {
        params: {
          cint: cint,
          locale: data?.locale,
          dwel: dwel,
          zip: zip,
          tdsp: tdsp,
          sessionId: sessionId,
          prom: prom,
          vendorId:
            vendorid === null || vendorid === undefined || vendorid === '' ? 'WEB' : vendorid,
        },
      });
      const enrolledPlan: Offers[] = plans?.data?.plans?.result?.offers?.filter(
        (plan: { id: string }) => plan.id === selectedPlan?.planId
      );
      if (enrolledPlan && enrolledPlan.length > 0) {
        let planIncentives: string[] = [];
        if (props.fields.IncentivesList !== undefined) {
          planIncentives = getPlanIncentives(
            enrolledPlan[0]?.incentiveId,
            props.fields.IncentivesList
          );
        }
        const details: SelectedPlanDetails = {
          planBenefits: enrolledPlan[0]?.planBenefits,
          planDisclaimer: enrolledPlan[0]?.planDisclaimer,
          incentiveId: enrolledPlan[0]?.incentiveId,
          incentiveDisclaimer: planIncentives.length > 0 ? planIncentives[1] : '',
          incentiveSpecialOfferText: planIncentives.length > 0 ? planIncentives[0] : '',
          oneLineSummary: enrolledPlan[0]?.oneLineSummary,
        };
        setSelectedPlanDetails(details);
      }
    };
    if (access_token !== '') {
      fetchData();
    }
  }, [selectedPlan, locale()]);

  // useEffect(() => {
  //   setSelectedPlanDetails(undefined);
  // }, [locale()]);

  if (selectedPlanDetails === undefined) {
    return (
      <div className="w-full h-[250px] flex justify-center">
        <Loader size="lg" />
      </div>
    );
  } else {
    return (
      <div>
        <Text
          tag="p"
          className="font-primaryBold text-textQuattuordenary text-[24px] leading-[30px]"
          field={props?.fields?.Heading}
        ></Text>
        <div className="flex flex-row sm:flex-row mt-[22px] rounded-[4px]">
          <div
            className={cn(
              'border-borderSeptendenary bg-bgNovemdenary w-full shadow-3xl rounded-b-xl rounded-xl',
              {
                'print:w-full': isAMB,
              }
            )}
          >
            <div className="w-full h-12 bg-bgVigintiunary flex justify-center items-center rounded-t-xl wide:m-auto ipad:m-auto ipad:max-w-full"></div>
            <div className="flex flex-col sm:flex-row p-6 w-full">
              <div className="w-full sm:w-1/2">
                <div className="flex flex-col sm:flex-col items-start sm:items-center">
                  <hr className="border-solid border-borderSeptendenary border-[1px] w-full my-6 sm:hidden order-2 sm:order-none print:hidden" />
                  <div className="sm:order-none">
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-plus2 sm:text-plus2"
                      field={{ value: selectedPlan?.planName }}
                    ></Text>
                  </div>
                  {/* <div className="border-l-2 border-borderOctodenary h-8 mx-6 sm:mx-8 hidden sm:block"></div> */}
                  <div className="flex flex-col sm:flex-row items-center mt-2 sm:mt-0 sm:order-none">
                    <div className="sm:ml-2 flex flex-col gap-1 sm:items-center">
                      <Text
                        tag="p"
                        className="font-primaryBold text-textQuattuordenary text-plus4"
                        field={{ value: (selectedPlan?.rate * 100).toFixed(1) + '\u00A2' }}
                      ></Text>
                      <Text
                        tag="p"
                        className="text-textQuattuordenary text-minus3 sm:text-minus2"
                        field={
                          dwel === '02' ? props?.fields?.PerkWhTextApt : props?.fields?.PerkWhText
                        }
                      ></Text>
                    </div>
                  </div>
                  <div className="flex-[2] flex flex-col gap-1">
                    <div className="flex flex-row">
                      <Text
                        tag="p"
                        className="font-primaryBold text-textQuattuordenary text-minus2 sm:ml-2"
                        field={{
                          value: selectedPlan?.term + ' ' + props.fields?.MonthsLabelText?.value,
                        }}
                      ></Text>
                      /
                      <Text
                        tag="p"
                        className="font-primaryBold text-textQuattuordenary text-minus2"
                        field={{ value: selectedPlan?.rateType }}
                      ></Text>
                    </div>
                  </div>

                  <div className="flex flex-row my-4 sm:my-10">
                    <p
                      onClick={() => setShowDetails((val) => !val)}
                      className="font-primaryBold text-minus3 sm:text-[18px] cursor-pointer flex flex-row justify-between sm:justify-normal items-center w-full "
                    >
                      {showDetails ? (
                        <Text field={props.fields?.HideDetailsText} />
                      ) : (
                        <Text field={props.fields?.ShowDetailsText} />
                      )}

                      <FontAwesomeIcon
                        className="sm:pl-1 pl-1"
                        icon={showDetails ? faChevronUpSolid : faChevronDownSolid}
                      />
                    </p>
                  </div>

                  {showDetails && (
                    <div className="flex flex-col sm:flex-row mt-2">
                      <div className="flex-[4] pr-10">
                        <RichText
                          className="font-primaryRegular text-textQuattuordenary text-minus3 leading-[20px] sm:text-minus2"
                          field={{ value: selectedPlanDetails?.oneLineSummary }}
                        ></RichText>
                      </div>
                    </div>
                  )}
                </div>

                {selectedPlanDetails?.incentiveSpecialOfferText && (
                  <span className="flex flex-row gap-2 items-center py-4">
                    <FontAwesomeIcon icon={faGift} size="sm" color="#9E1E62" />
                    <span className="flex-none font-primaryBold">
                      {selectedPlanDetails?.incentiveSpecialOfferText}
                    </span>
                  </span>
                )}
                <div className="flex sm:hidden print:hidden">
                  <Text
                    tag="p"
                    className="font-primaryBold text-textQuattuordenary text-minus3 sm:text-minus1 print:hidden"
                    field={props.fields?.NeedHelpText}
                  ></Text>
                  <Text
                    tag="p"
                    className="font-primaryBold text-textPrimary hover:text-textSecondary text-minus3 sm:text-minus1 ml-[8px]"
                    field={props.fields?.NeedHelpPhNumber}
                  ></Text>
                </div>
              </div>

              <div className="w-full sm:w-1/2">
                <div
                  className={cn(
                    'flex gap-2 sm:gap-[105px] flex-col sm:flex-col sm:justify-start pt-0',
                    { 'print:bg-white': isAMB }
                  )}
                >
                  <div className="flex flex-col gap-4 w-full sm:w-80 text-textQuattuordenary">
                    <RichText
                      tag="span"
                      className="text-[14px] font-primaryRegular list-disc-custom breakSeparator"
                      field={{ value: selectedPlanDetails?.planBenefits?.toString() }}
                    ></RichText>
                    <RichText
                      tag="span"
                      className="text-sm font-primaryRegular"
                      field={{ value: selectedPlanDetails?.planDisclaimer?.toString() }}
                    ></RichText>
                    <RichText
                      tag="span"
                      className="text-sm font-primaryRegular"
                      field={{ value: selectedPlanDetails?.incentiveDisclaimer?.toString() }}
                    ></RichText>
                  </div>

                  {showDetails && (
                    <div className="flex flex-col gap-4 custom-download">
                      <Link
                        target="_blank"
                        field={{
                          value: { href: selectedPlan?.EFLUrl },
                        }}
                        className={
                          'text-textPrimary  hover:text-textSecondary flex flex-row text-minus1 font-primaryBold items-center'
                        }
                      >
                        {props.fields?.ElectricityFactsLabelText?.value}
                        <span className="pl-2">
                          <DownloadIcon />
                        </span>
                      </Link>
                      <Link
                        target="_blank"
                        field={{ value: { href: selectedPlan?.TOSUrl } }}
                        className={
                          'text-textPrimary hover:text-textSecondary flex flex-row text-minus1 font-primaryBold items-center'
                        }
                      >
                        {props.fields?.TermsOfServiceText?.value}
                        <span className="pl-2">
                          <DownloadIcon />
                        </span>
                      </Link>
                      <Link
                        target="_blank"
                        field={{ value: { href: selectedPlan?.YRCUrl } }}
                        className={
                          'text-textPrimary hover:text-textSecondary flex flex-row text-minus1 font-primaryBold items-center'
                        }
                      >
                        {props.fields?.YourRightsAsaCustomerText?.value}
                        <span className="pl-2">
                          <DownloadIcon />
                        </span>
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export { SelectedPlanCardWithDetails };
const Component = withDatasourceCheck()<SelectedPlanCardWithDetailsProps>(
  SelectedPlanCardWithDetails
);

export const getServerSideProps: GetServerSideComponentProps = async (
  _rendering,
  _layoutData,
  context
) => {
  const sessionid = v4();
  const access_token = context.req.session.user?.access_token;
  const locale = context.locale as string;
  if (access_token) {
    return { sessionid, access_token, locale };
  } else {
    return { redirect: '/oops' };
  }
};

export default aiLogger(Component, Component.name);
