import { faChevronLeft, faMobileAndroidAlt } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Link, LinkField, Placeholder, Text } from '@sitecore-jss/sitecore-jss-nextjs';
import Mobile from 'assets/icons/Mobile';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';

type TEEHeaderProps = ComponentProps & {
  fields: {
    BackToSiteText: Field<string>;
    BackToSiteLink: LinkField;
    NeedHelpText: Field<string>;
    MobileNumber: Field<string>;
  };
};

const TEEHeader = (props: TEEHeaderProps): JSX.Element => {
  return (
    <div className="bg-transparent">
      <Placeholder
        name="jss-pageheader"
        rendering={props.rendering}
        render={(components) => (
          <div className="flex items-center justify-center min-h-[68px] sm:min-h-[85px] px-3 sm:px-[80px] sm:h-[120px] sm:max-w-[830px] sm:ml-[420px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6 sm:pl-0 sm:pr-0 ipad:px-4 wide:px-4">
            <div className=" -ml-[9rem] sm:ml-0 lg:-mt-7 sm:-mt-0 md:mt-[20px]">{components}</div>
            <div className="ml-auto text-right  sm:block ipad:hidden wide:hidden">
              {/* <span className="font-primaryBold text-textQuinary text-minus1">
                <a
                  href={props.fields?.BackToSiteLink?.value?.href}
                  className="flex items-center gap-3 font-primaryBold sm:text-minus1"
                >
                  <FontAwesomeIcon
                    icon={faChevronLeft}
                    className="mt-[5px] text-lg text-textQuinary"
                  />
                  <Text field={props.fields?.BackToSiteText} />
                </a>
              </span> */}
              <div className="text-minus2 mt-3">
                <span className="text-textQuinary font-primaryRegular hidden">
                  <Text
                    tag="span"
                    className="text-textQuinary font-primaryRegular sm:text-minus1 hidden"
                    field={props.fields?.NeedHelpText}
                  />
                </span>
                <span className="font-primaryBold text-base text-textQuinary">
                  <Link
                    href={`tel:${props.fields.MobileNumber.value}`}
                    className="flex items-center gap-3 text-textQuinary pl-1 font-primaryBold sm:text-base"
                    aria-label={`Call customer service at ${props.fields?.MobileNumber?.value}`}
                    field={{ value: props.fields?.MobileNumber?.value }}
                  >
                    <span className="text-textQuinary mt-1 text-base block">
                      <Mobile />
                    </span>
                    {props.fields?.MobileNumber?.value}
                  </Link>
                </span>
              </div>
            </div>
          </div>
        )}
      />
    </div>
  );
};

export { TEEHeader };
export default aiLogger(TEEHeader, TEEHeader.name);
