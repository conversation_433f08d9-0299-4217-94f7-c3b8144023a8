import { NextApiRequest, NextApiResponse } from 'next';
import crypto from 'node:crypto';
import { XMLParser } from 'fast-xml-parser';
import path from 'path';
import { promises as fs } from 'fs';
import PaymentAPI from 'src/services/PaymentAPI';
import { withSessionApiRoute } from 'lib/with-session';

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
async function handler(req: NextApiRequest, res: NextApiResponse) {
  const language = req.query['lang'] as string;
  const access_token = req.session.user?.access_token;
  if (access_token) {
    const jsonDirectory = path.join(process.cwd(), 'Paymetric');
    //Read the json data file data.json
    let paymentXML = '';

    if (process.env.PAYMETRIC_ENV_PROD === 'production') {
      if (language === 'es') {
        paymentXML = await fs.readFile(jsonDirectory + '/Prod/paymetric_es.xml', 'utf8');
      } else if (language === 'en') {
        paymentXML = await fs.readFile(jsonDirectory + '/Prod/paymetric.xml', 'utf8');
      }
    } else if (process.env.PAYMETRIC_ENV_PROD === 'non-prod') {
      if (language === 'es') {
        paymentXML = await fs.readFile(jsonDirectory + '/NonProd/paymetric_es.xml', 'utf8');
      } else if (language === 'en') {
        paymentXML = await fs.readFile(jsonDirectory + '/NonProd/paymetric.xml', 'utf8');
      }
    } else if (process.env.PAYMETRIC_ENV_PROD === 'local') {
      if (language === 'es') {
        paymentXML = await fs.readFile(jsonDirectory + '/Local/paymetric_es.xml', 'utf8');
      } else if (language === 'en') {
        paymentXML = await fs.readFile(jsonDirectory + '/Local/paymetric.xml', 'utf8');
      }
    } else if (process.env.PAYMETRIC_ENV_PROD === 'staging') {
      if (language === 'es') {
        paymentXML = await fs.readFile(jsonDirectory + '/Staging/paymetric_es.xml', 'utf8');
      } else if (language === 'en') {
        paymentXML = await fs.readFile(jsonDirectory + '/Staging/paymetric.xml', 'utf8');
      }
    }
    console.log(paymentXML);
    switch (req.method) {
      case 'GET': {
        // const paymentAPI = new PaymentAPI();
        const sharedKey = process.env.PaymetricSharedKey as string;
        const signature = crypto
          .createHmac('sha256', sharedKey)
          .update(paymentXML)
          .digest('base64');
        const postData = `MerchantGuid=${
          process.env.PAYMETRIC_MERCHANT_GUID
        }&SessionRequestType=01&Signature=${encodeURIComponent(
          signature
        )}&MerchantDevelopmentEnvironment=node&Packet=${encodeURIComponent(paymentXML)}`;

        const accessToken = await PaymentAPI.generateAccessToken(postData);
        const parser = new XMLParser();
        const accesstokenObj = parser.parse(accessToken.data);
        console.log(accesstokenObj);
        res
          .status(200)
          .send({ message: accesstokenObj.AccessTokenResponsePacket.ResponsePacket.AccessToken });
      }
      default: {
        res.status(405).end();
      }
    }
  } else {
    res.status(401).end();
  }
}

export default withSessionApiRoute(handler);
