import modals from 'src/utils/modals';

interface SubmitOptions {
  iFrameId: string;
  targetUrl: string;
  validation?: () => Promise<void>;
  onSuccess?: (msg: string) => Promise<void>;
  onError?: (msg: string) => void;
}

interface SVCOptions {
  baseLiveAgentContentURL: string;
  deploymentId: string;
  buttonId: string;
  baseLiveAgentURL: string;
  eswLiveAgentDevName: string;
  isOfflineSupportEnabled: boolean;
}

interface SVCSettings {
  disabledMinimizedText: string;
  displayHelpButton: boolean;
  language: string;
  enabledFeatures: string[];
  entryFeature: string;
  defaultMinimizedText: string;
  prepopulatedPrechatFields: Record<string, string>;
  extraPrechatFormDetails: extraPrechatFormDetails[];
  extraPrechatInfo: extraPrechatInfo[];
  prechatBackgroundImgURL: string;
  smallCompanyLogoImgURL: string;
  waitingStateBackgroundImgURL: string;
}

interface extraPrechatFormDetails {
  label: string;
  name?: string;
  value: string;
  transcriptFields?: string[];
  displayToAgent: boolean;
}

interface extraPrechatInfo {
  entityName: string;
  showOnCreate: boolean;
  linkToEntityName?: string;
  linkToEntityField?: string;
  saveToTranscript?: string;
  entityFieldMaps: entityFieldMaps[];
}
interface entityFieldMaps {
  isExactMatch: boolean;
  fieldName: string;
  doCreate: boolean;
  doFind: boolean;
  label: string;
}

interface Translation {
  fields: {
    Message: {
      value: string;
    };
  };
}

interface Genesys {
  widgets: {
    main: {
      debug: boolean;
      theme: string;
      lang: string;
      i18n: string;
      customStylesheetID: string;
      preload: unknown[];
    };
    webchat: {
      userData: {
        SAPCADCALLERNEED: string;
        SAPCADLANGUAGE: string;
        SAPCADACCTNUM: string | null;
        SAPCADBPNUM: string | null;
        SAPCADIDENTIFIED: string;
        SAPCADAUTH: string;
      };
      emojis: boolean;
      cometD: {
        enabled: boolean;
      };
      autoInvite: {
        enabled: boolean;
        timeToInviteSeconds: number;
        inviteTimeoutSeconds: number;
      };
      chatButton: {
        enabled: boolean;
        openDelay: number;
        effectDuration: number;
        hideDuringInvite: boolean;
        template?: string;
        effect?: string;
      };
      uploadsEnabled: boolean;
      dataURL: string;
      pluginMap: {
        richmediabridge: string;
        webchatservice: string;
        webchatservicelegacymod: string;
      };
      form: {
        inputs: InputField[] | null;
      };
    };
    init?: () => void;
  };
}

interface InputField {
  id: string;
  name: string;
  maxlength: string | null;
  placeholder: string | null;
  label: string;
  required: boolean;
  type: string;
  options: DropDownOption[] | null;
}

interface DropDownOption {
  value: string;
  label: string;
  selected: boolean | false;
}

interface StateItem {
  displayName: Field<string>;
  id: Field<string>;
  name: string;
  url: Field<string>;
  fields: {
    Name: Field<string>;
    Value: Field<string>;
  };
}

export interface ActionResponse {
  actionToken: string;
}

interface ActionEventOptions {
  correlationId: string;
  claimedUserId: string;
}

export declare global {
  interface Window {
    $XIFrame: {
      submit: (options: SubmitOptions) => void;
    };
    embedded_svc: {
      init: (
        FirstInitUrl: string,
        SecondInitUrl: string,
        gslbBaseURL: string,
        SkillID: string,
        SkillName: string,
        options: SVCOptions
      ) => void;
      settings: SVCSettings;
    };
    adobeDataLayer: {
      phoneNumber: string;
      FirstName: string;
      LastName: string;
      EmailAddress: string;
      ESIID: string;
      HMC: { [key: string]: string };
      PaymentType: string;
      ChangeAddress: string;
      TransferAddress: string;
      ExperienceID: string;
    };
    tsPlatform: {
      drs: {
        triggerActionEvent: (actionType: string, ActionEventOptions) => Promise<ActionResponse>;
      };
    };
    _genesys: Genesys;
    _gt: GTEvent[];

    CXBus?: {
      command: (commandName: string, args?: Record<string, any>) => Promise<any> | void;
      subscribe: (eventName: string, callback: (data: any) => void) => void;
      unsubscribe: (eventName: string, callback: (data: any) => void) => void;
      configure?: (config: Record<string, any>) => void;
      loadPlugin?: (pluginName: string) => void;
    };
  }
}

declare module '@mantine/modals' {
  export interface MantineModalsOverride {
    modals: typeof modals;
  }
}
